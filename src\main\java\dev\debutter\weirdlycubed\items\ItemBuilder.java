package dev.debutter.weirdlycubed.items;

import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;
import net.minestom.server.component.DataComponents;
import net.minestom.server.item.ItemStack;
import net.minestom.server.item.Material;
import net.minestom.server.item.component.CustomData;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

public abstract class ItemBuilder {

    /** The item tag used inside the CustomData component to denote the id of the custom item */
    public static final Tag<String> ITEM_TAG = Tag.String("custom_item");

    protected abstract @NotNull String id();
    protected abstract @NotNull Material material();
    protected abstract @Nullable Component name();
    protected abstract @Nullable ItemRarity rarity();
    protected abstract @Nullable List<ItemType> types();
    protected abstract @Nullable Component description();
    protected abstract @Nullable CompoundBinaryTag.Builder data();

    protected final ItemStack.Builder generate() {
        ItemStack.Builder builder = ItemStack
            .builder(material())
            .lore(lore())
            .set(DataComponents.CUSTOM_DATA, customData());

        Component name = name();
        if (name != null) builder.set(DataComponents.ITEM_NAME, name);

        return builder;
    }

    private CustomData customData() {
        CompoundBinaryTag.Builder dataCompound = data();
        if (dataCompound == null) dataCompound = CompoundBinaryTag.builder();

        return new CustomData(
            dataCompound
                .putString(ITEM_TAG.getKey(), id())
                .build()
        );
    }
    private List<Component> lore() {
        List<Component> loreLines = new ArrayList<>();

        ItemRarity rarity = rarity();
        List<ItemType> types = types();
        Component description = description();

        if (rarity != null) {
            loreLines.add(AwesomerText.beautifyMessage("<italic:false><gray>Rarity: <stars>",
                Placeholder.component("stars", rarity.getComponent())
            ));
        }
        if (types != null) {
            // Join item types
            Component body = AwesomerText.beautifyMessage("");

            for (int i = 0; i < types.size(); i++) {
                Component typeComponent = types.get(i).getComponent();

                body = body.append(typeComponent);

                if (i != types.size() - 1) body = body.append(AwesomerText.beautifyMessage("<dark_gray> | </dark_gray>"));
            }

            // Append type lore
            loreLines.add(AwesomerText.beautifyMessage("<italic:false><gray>Type: <types>",
                Placeholder.component("types", body)
            ));
        }
        if (description != null) {
            loreLines.add(Component.empty());

            // TODO: make sure new lines in the description are indented with two spaces
            loreLines.add(AwesomerText.beautifyMessage("<italic:false><dark_gray>⏵ <gray><description></gray>",
                Placeholder.component("description", description)
            ));
        }

        return loreLines;
    }

}
