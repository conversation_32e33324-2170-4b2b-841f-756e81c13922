package dev.debutter.weirdlycubed.server;

import dev.debutter.weirdlycubed.Main;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import io.vertx.ext.web.RoutingContext;
import net.kyori.adventure.resource.ResourcePackInfo;
import net.kyori.adventure.resource.ResourcePackRequest;
import net.minestom.server.entity.Player;
import net.minestom.server.event.player.PlayerResourcePackStatusEvent;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.net.URI;
import java.util.UUID;

import static net.kyori.adventure.resource.ResourcePackRequest.resourcePackRequest;

public class ResourcePackHandler {

    public static final File resourcePackFile = new File("./cache/pack.zip");
    private static final URI PUBLIC_URI = URI.create(Main.getConfig().getString("web.public-url"));
    private static final ResourcePackInfo INFO = ResourcePackInfo.resourcePackInfo(UUID.randomUUID(), PUBLIC_URI.resolve("/resource-pack.zip"), "i am not a hash!");
    private static final ResourcePackRequest REQUEST = resourcePackRequest()
        .required(true)
        .packs(INFO)
        .prompt(LocaleManager.getBeautifiedMessage("server.resource-pack-prompt"))
        .build();

    public static void onResourcePackStatus(@NotNull PlayerResourcePackStatusEvent event) {
        ConsoleLogger.info(event.getStatus().toString());
    }

    public static void sendResourcePack(Player player) {
        player.sendResourcePacks(REQUEST);
    }

    public static void serveResourcePack(RoutingContext ctx) {
        ctx.response().sendFile(resourcePackFile.getAbsolutePath());
    }

}
