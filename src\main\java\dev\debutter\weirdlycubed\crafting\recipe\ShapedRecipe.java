package dev.debutter.weirdlycubed.crafting.recipe;

import dev.debutter.weirdlycubed.crafting.matrix.CraftingMatrix;
import dev.debutter.weirdlycubed.crafting.matrix.ReferenceMatrix;
import dev.debutter.weirdlycubed.crafting.matrix.Slot;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.ItemHelper;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ShapedRecipe implements CraftingRecipe {

    private final ReferenceMatrix items;
    private final CustomItemTypes output;

    public ShapedRecipe(@NotNull CustomItemTypes output, @NotNull ReferenceMatrix items) {
        this.items = items;
        this.output = output;
    }

    @NotNull
    @Override
    public ItemStack output() {
        return output.getItemStack();
    }

    @Override
    public boolean match(CraftingMatrix matrix) {
        for (Slot slot : Slot.values()) {
            @Nullable CustomItemTypes referenceItem = items.getSlot(slot);
            ItemStack inputItem = matrix.getSlot(slot);

            if (referenceItem == null) {
                if (!inputItem.equals(ItemStack.AIR)) {
                    return false;
                }
            } else {
                if (ItemHelper.getCustomItem(inputItem) != referenceItem) {
                    return false;
                }
            }
        }

        return true;
    }

}
