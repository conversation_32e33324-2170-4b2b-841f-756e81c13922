plugins {
    id 'java'
    id "com.gradleup.shadow" version "8.3.3"
    id "net.kyori.blossom" version "1.3.0"
}

group = project.project_group
version = project.project_version

repositories {
    mavenCentral()
    maven { url 'https://repo.spongepowered.org/maven' }
    maven { url 'https://repo.velocitypowered.com/snapshots/' }
    maven { url 'https://jitpack.io' }
    maven { url 'https://repo1.maven.org/maven2/net/minestom/minestom-snapshots/' } // repo.minestom.net/ has been removed due to it literally not existing.
    maven { url "https://repo.hypera.dev/snapshots/" }
    maven { url "https://repo.lucko.me/" }
    maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
}

dependencies {
    testImplementation platform('org.junit:junit-bom:5.9.1')
    testImplementation 'org.junit.jupiter:junit-jupiter'

    // noise
    implementation "org.spongepowered:noise:${sponge_noise_version}"

    // yaml
    implementation "dev.dejvokep:boosted-yaml:${boosted_yaml_version}"

    // adventure
    implementation "net.kyori:adventure-text-minimessage:${adventure_version}"
    implementation "net.kyori:adventure-text-serializer-ansi:${adventure_version}"

    // minestom
    implementation "net.minestom:minestom-snapshots:${minestom_version}"

    // vertx
    implementation platform("io.vertx:vertx-stack-depchain:${vertx_version}")
    implementation "io.vertx:vertx-core"
    implementation "io.vertx:vertx-web"

    // spark
    implementation("dev.lu15:spark-minestom:${spark_version}")
}

blossom {
    replaceTokenIn('src/main/java/dev/debutter/weirdlycubed/Constants.java')
    replaceToken '@project_name@', project.project_name
    replaceToken '@version@', version
}

test {
    useJUnitPlatform()
}

java {
    toolchain.languageVersion.set(JavaLanguageVersion.of(project.java_version))

    sourceCompatibility = project.java_version;
    targetCompatibility = project.java_version;

//    withJavadocJar()
//    withSourcesJar()
}

tasks {
    jar {
        manifest {
            attributes["Main-Class"] = "dev.debutter.weirdlycubed.Main"
        }
    }
    build {
        dependsOn ":shadowJar"
    }
    test {
        useJUnitPlatform()
    }
    shadowJar {
        mergeServiceFiles()
        archiveClassifier.set("") // Prevent the -all suffix
    }
}
