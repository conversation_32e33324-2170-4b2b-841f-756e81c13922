package dev.debutter.weirdlycubed.player;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import dev.debutter.weirdlycubed.instances.WorldManager;
import dev.debutter.weirdlycubed.player.data.PlayerStorage;
import dev.debutter.weirdlycubed.player.data.PlayerMode;
import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.server.BannedPlayer;
import dev.debutter.weirdlycubed.server.ResourcePackHandler;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;
import net.minestom.server.MinecraftServer;
import net.minestom.server.adventure.audience.Audiences;
import net.minestom.server.coordinate.Pos;
import net.minestom.server.entity.Player;
import net.minestom.server.entity.attribute.Attribute;
import net.minestom.server.event.player.AsyncPlayerConfigurationEvent;
import net.minestom.server.event.player.PlayerDisconnectEvent;
import net.minestom.server.event.player.PlayerSpawnEvent;
import net.minestom.server.timer.TaskSchedule;
import org.jetbrains.annotations.NotNull;

import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Random;
import java.util.UUID;

public class PlayerConnection {

    public static void onPlayerJoin(@NotNull PlayerSpawnEvent event) {
        Player player = event.getPlayer();
        String username = player.getUsername();
        UUID uuid = player.getUuid();
        ConsoleLogger.logPlayerJoin(username);

        // Load player data
        PlayerStorage.loadPlayerData(player);

        PlayerRuntimeData.setPlayerMode(player, PlayerMode.SURVIVAL);

        // Check if player is banned
        BannedPlayer bannedPlayer = BannedPlayer.getBannedPlayer(uuid);
        if (bannedPlayer != null) {
            player.kick(AwesomerText.beautifyMessage("<red>You are banned: " + bannedPlayer.reason()));
            return;
        }

        // Send a random join message
        try {
            Type listType = new TypeToken<List<String>>() {}.getType();
            List<String> welcomeMessages = new Gson().fromJson(new FileReader("config/welcomemsg.json"), listType);

            String welcomeMessage = welcomeMessages.get(new Random().nextInt(welcomeMessages.size()));
            welcomeMessage = welcomeMessage.replace("<player_name>", username);

            Audiences.players().sendMessage(
                    AwesomerText.beautifyMessage(
                            welcomeMessage,
                            Placeholder.unparsed("player_name", username)
                    )
            );
        } catch (IOException e) {
            ConsoleLogger.exception(e);
        }

        // Apply resource pack after a short delay
        MinecraftServer.getSchedulerManager().scheduleTask(() -> {
            ResourcePackHandler.sendResourcePack(player);
        }, TaskSchedule.tick(20), TaskSchedule.stop());
    }

    public static void onPlayerDisconnect(@NotNull PlayerDisconnectEvent event) {
        Player player = event.getPlayer();
        String username = player.getUsername();

        ConsoleLogger.logPlayerDisconnect(username);
        Audiences.players().sendMessage(
                AwesomerText.beautifyMessage(
                        "<rainbow><username> has left the server!</rainbow>",
                        Placeholder.unparsed("username", username)
                )
        );

        // Save player data to disk
        if (!PlayerStorage.savePlayerData(player)) {
            ConsoleLogger.warning("Something went wrong whilst saving the player data of " + player.getUsername());
        }
    }

    public static void onPlayerConfiguration(@NotNull AsyncPlayerConfigurationEvent event) {
        event.setSpawningInstance(WorldManager.getInstanceContainer());

        Player player = event.getPlayer();
        player.setRespawnPoint(new Pos(0, 62, 0));
        player.getAttribute(Attribute.BLOCK_BREAK_SPEED).setBaseValue(0);

        ConsoleLogger.logPlayerConnect(player.getUsername());
    }

}