package dev.debutter.weirdlycubed.crafting.recipe;

import dev.debutter.weirdlycubed.crafting.matrix.CraftingMatrix;
import dev.debutter.weirdlycubed.crafting.matrix.Slot;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.ItemHelper;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class ShapelessRecipe implements CraftingRecipe {

    private CustomItemTypes output;
    private CustomItemTypes[] items;

    public ShapelessRecipe(@NotNull CustomItemTypes output, @NotNull CustomItemTypes ...items) {
        this.output = output;
        this.items = items;
    }

    @NotNull
    @Override
    public ItemStack output() {
        return output.getItemStack();
    }

    @Override
    public boolean match(CraftingMatrix matrix) {
        ArrayList<CustomItemTypes> referenceItems = new ArrayList<>(List.of(items));

        for (Slot slot : Slot.values()) {
            ItemStack inputItem = matrix.getSlot(slot);
            if (inputItem.equals(ItemStack.AIR)) continue;

            CustomItemTypes e = ItemHelper.getCustomItem(inputItem);

            if (referenceItems.contains(e)) {
                referenceItems.remove(e);
            } else {
                return false;
            }
        }

        return referenceItems.isEmpty();
    }

}
