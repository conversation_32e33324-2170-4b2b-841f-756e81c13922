package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.PermissionsHandler;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.Argument;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.entity.Player;

public class KickCmd extends Command {

    public KickCmd() {
        super("kick");

        setDefaultExecutor((sender, context) ->
                sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.kick.usage"))
        );

        Argument<String> playerArgument = ArgumentType.String("player");
        Argument<String> reasonArgument = ArgumentType.String("reason")
                .setDefaultValue("Kicked by an operator"); // default reason

        addSyntax((sender, context) -> {
            if (!PermissionsHandler.isOp(sender.identity().uuid())) {
                sender.sendMessage(Component.text("You do not have permission to use this command.", NamedTextColor.RED));
                return;
            }

            String playerName = context.get(playerArgument);
            String reason = context.get(reasonArgument);

            Player target = null;
            for (Player p : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
                if (p.getUsername().equalsIgnoreCase(playerName)) {
                    target = p;
                    break;
                }
            }

            if (target != null) {
                target.kick(Component.text("You have been kicked: " + reason, NamedTextColor.RED));
                sender.sendMessage(Component.text("Kicked player '" + playerName + "'. Reason: " + reason, NamedTextColor.GREEN));
            } else {
                sender.sendMessage(Component.text("Player '" + playerName + "' not found.", NamedTextColor.RED));
            }
        }, playerArgument, reasonArgument);
    }
}
