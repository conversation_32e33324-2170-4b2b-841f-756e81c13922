package dev.debutter.weirdlycubed.crafting.matrix;

import dev.debutter.weirdlycubed.items.CustomItemTypes;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;

public class ReferenceMatrix implements Cloneable {

    private final CustomItemTypes[] items = new CustomItemTypes[9];

    /**
     * Sets a given slot to a custom item type
     * @return A reference to this object
     */
    public ReferenceMatrix setSlot(Slot slot, CustomItemTypes item) {
        items[slot.getIndex()] = item;
        return this;
    }

    public @Nullable CustomItemTypes getSlot(Slot slot) {
        return items[slot.getIndex()];
    }

    @Override
    public ReferenceMatrix clone() {
        try {
            return (ReferenceMatrix) super.clone();
        } catch (Exception e) {
            ReferenceMatrix copy = new ReferenceMatrix();

            System.arraycopy(items, 0, copy.items, 0, 9);

            return copy;
        }
    }

    @Override
    public String toString() {
        return "ReferenceMatrix" + Arrays.toString(items);
    }

}
