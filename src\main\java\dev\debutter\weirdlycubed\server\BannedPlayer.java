package dev.debutter.weirdlycubed.server;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import org.jetbrains.annotations.Nullable;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

public record BannedPlayer(String uuid, String reason, String created, String expires) {

    private static final String filepath = "config/banned-players.json";

    public static BannedPlayer[] getBannedPlayers() {
        try {
            return new Gson().fromJson(new FileReader(filepath), BannedPlayer[].class);
        } catch (IOException e) {
            ConsoleLogger.exception(e);
            return new BannedPlayer[0];
        }
    }

    public static @Nullable BannedPlayer getBannedPlayer(UUID uuid) {
        for (BannedPlayer bannedPlayer : getBannedPlayers()) {
            if (bannedPlayer.uuid().equals(uuid.toString())) {
                return bannedPlayer;
            }
        }

        return null;
    }

    public static void banPlayer(UUID uuid, String reason) {
        BannedPlayer[] bannedPlayers = getBannedPlayers();

        // Cancel if the player has already been banned
        for (BannedPlayer bannedPlayer : bannedPlayers) {
            if (bannedPlayer.uuid().equals(uuid.toString())) {
                return;
            }
        }

        // Save the new list of banned players
        BannedPlayer[] newBannedPlayers = new BannedPlayer[bannedPlayers.length + 1];
        BannedPlayer bannedPlayer = new BannedPlayer(
                uuid.toString(),
                reason,
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                "never"
        );

        newBannedPlayers[bannedPlayers.length] = bannedPlayer;

        try (Writer writer = new FileWriter(filepath)) {
            Gson gson = new GsonBuilder().create();
            gson.toJson(newBannedPlayers, writer);
        } catch (IOException e) {
            ConsoleLogger.exception(e);
        }
    }

}
