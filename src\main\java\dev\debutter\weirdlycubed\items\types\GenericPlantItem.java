package dev.debutter.weirdlycubed.items.types;

import dev.debutter.weirdlycubed.items.ItemBuilder;
import dev.debutter.weirdlycubed.items.ItemRarity;
import dev.debutter.weirdlycubed.items.ItemType;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.text.Component;
import net.minestom.server.item.Material;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class GenericPlantItem extends ItemBuilder {

    private final String itemId;
    private final Material itemMaterial;
    private final Component itemName;
    private final ItemRarity rarity;

    public GenericPlantItem(String itemId, @NotNull Material itemMaterial, @NotNull String itemName, ItemRarity rarity) {
        this.itemId = itemId;
        this.itemMaterial = itemMaterial;
        this.itemName = AwesomerText.beautifyMessage(itemName);
        this.rarity = rarity;
    }

    @NotNull
    @Override
    public String id() {
        return itemId;
    }

    @NotNull
    @Override
    protected Material material() {
        return itemMaterial;
    }

    @Nullable
    @Override
    protected Component name() {
        return itemName;
    }

    @Nullable
    @Override
    public ItemRarity rarity() {
        return rarity;
    }

    @Nullable
    @Override
    public List<ItemType> types() {
        return List.of(ItemType.PLANT);
    }

    @Nullable
    @Override
    public Component description() {
        return null;
    }

    @Nullable
    @Override
    protected CompoundBinaryTag.Builder data() {
        return null;
    }
}
