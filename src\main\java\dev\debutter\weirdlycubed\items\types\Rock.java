package dev.debutter.weirdlycubed.items.types;

import dev.debutter.weirdlycubed.items.ItemBuilder;
import dev.debutter.weirdlycubed.items.ItemRarity;
import dev.debutter.weirdlycubed.items.ItemType;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.text.Component;
import net.minestom.server.item.Material;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class Rock extends ItemBuilder {

    @NotNull
    @Override
    public String id() {
        return "rock";
    }

    @NotNull
    @Override
    protected Material material() {
        return Material.STONE_BUTTON;
    }

    @Nullable
    @Override
    protected Component name() {
        return AwesomerText.beautifyMessage("<#a8b0bd>Rock</#a8b0bd>");
    }

    @Nullable
    @Override
    public ItemRarity rarity() {
        return ItemRarity.COMMON;
    }

    @Nullable
    @Override
    public List<ItemType> types() {
        return List.of(ItemType.MATERIAL);
    }

    @Nullable
    @Override
    public Component description() {
        return null;
    }

    @Nullable
    @Override
    protected CompoundBinaryTag.Builder data() {
        return null;
    }
}
