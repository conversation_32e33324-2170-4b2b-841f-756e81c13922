package dev.debutter.weirdlycubed.crafting.matrix;

import java.util.Arrays;
import java.util.List;

public enum Slot {

    TOP_LEFT(0),
    TOP_MIDDLE(1),
    TOP_RIGHT(2),
    MIDDLE_LEFT(3),
    MIDDLE_MIDDLE(4),
    MIDDLE_RIGHT(5),
    BOTTOM_LEFT(6),
    BOTTOM_MIDDLE(7),
    BOTTOM_RIGHT(8);

    // Predefined boundary slots
    public static List<Slot> topSlots = List.of(Slot.TOP_LEFT, Slot.TOP_MIDDLE, Slot.TOP_RIGHT);
    public static List<Slot> bottomSlots = List.of(Slot.BOTTOM_LEFT, Slot.BOTTOM_MIDDLE, Slot.BOTTOM_RIGHT);
    public static List<Slot> leftSlots = List.of(Slot.TOP_LEFT, Slot.MIDDLE_LEFT, Slot.BOTTOM_LEFT);
    public static List<Slot> rightSlots = List.of(Slot.TOP_RIGHT, Slot.MIDDLE_RIGHT, Slot.BOTTOM_RIGHT);
    // Precomputed negated boundary slots
    public static List<Slot> notTopSlots = Arrays.stream(Slot.values()).filter(s -> Slot.topSlots.contains(s)).toList();
    public static List<Slot> notBottomSlots = Arrays.stream(Slot.values()).filter(s -> Slot.bottomSlots.contains(s)).toList();
    public static List<Slot> notLeftSlots = Arrays.stream(Slot.values()).filter(s -> Slot.leftSlots.contains(s)).toList();
    public static List<Slot> notRightSlots = Arrays.stream(Slot.values()).filter(s -> Slot.rightSlots.contains(s)).toList();

    private final int index;

    Slot(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }
}
