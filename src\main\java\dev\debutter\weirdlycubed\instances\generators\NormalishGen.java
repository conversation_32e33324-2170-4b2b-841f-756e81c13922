package dev.debutter.weirdlycubed.instances.generators;

import dev.debutter.weirdlycubed.instances.generators.features.Biome;
import dev.debutter.weirdlycubed.instances.generators.features.Desert;
import dev.debutter.weirdlycubed.instances.generators.features.Grassless;
import dev.debutter.weirdlycubed.instances.generators.features.Hills;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.generator.GenerationUnit;
import org.jetbrains.annotations.NotNull;
import org.spongepowered.noise.module.source.RidgedMultiSimplex;
import org.spongepowered.noise.module.source.Simplex;
import org.spongepowered.noise.module.source.Voronoi;

import java.util.List;
import java.util.Random;

public class NormalishGen extends WorldGen {

    private final Simplex terrainNoise;
    private final RidgedMultiSimplex ridgedNoise;
    private final int waterLevel = 62;

    private final List<Biome> biomes;
    private final Voronoi biomeCellNoise;
    private final Simplex biomeWobbleNoise;
    private final Simplex biomeDistortNoise;

    public NormalishGen(long seed) {
        super(seed);

        Random rng = new Random(seed);

        this.terrainNoise = new Simplex();
        this.terrainNoise.setSeed(rng.nextInt());
        this.terrainNoise.setFrequency(0.0015);
        this.terrainNoise.setOctaveCount(5);

        this.ridgedNoise = new RidgedMultiSimplex();
        this.ridgedNoise.setSeed(rng.nextInt());
        this.ridgedNoise.setFrequency(0.02);
        this.ridgedNoise.setOctaveCount(2);

        this.biomeCellNoise = new Voronoi();
        this.biomeCellNoise.setSeed(rng.nextInt());
        this.biomeCellNoise.setFrequency(0.003);

        this.biomeWobbleNoise = new Simplex();
        this.biomeWobbleNoise.setSeed(rng.nextInt());
        this.biomeWobbleNoise.setOctaveCount(2);
        this.biomeWobbleNoise.setFrequency(0.03);

        this.biomeDistortNoise = new Simplex();
        this.biomeDistortNoise.setSeed(rng.nextInt());
        this.biomeDistortNoise.setOctaveCount(2);
        this.biomeDistortNoise.setFrequency(0.03);

        this.biomes = List.of(
            new Hills(rng.nextInt()),
            new Desert(rng.nextInt()),
            new Grassless(rng.nextInt())
        );
    }

    @Override
    public void generate(@NotNull GenerationUnit unit) {
        Point start = unit.absoluteStart();

        for (int x = 0; x < unit.size().x(); x++) {
            for (int z = 0; z < unit.size().z(); z++) {
                Point bottom = start.add(x, 0, z);
                double height = getHeightAt(bottom.x(), bottom.z());

                Biome biome = getBlendedBiome(bottom.x(), bottom.z());

                boolean favorLower = (height - Math.floor(height)) < 0.5;
                int stoneHeight = Math.max((int) height, 0);

                biome.generateTerrain(unit, bottom, stoneHeight, waterLevel, favorLower);
            }
        }
    }

    private double getHeightAt(double x, double z) {
        double height = terrainNoise.get(x * 2, 0, z * 2) * 52;
        if (height > waterLevel) {
            height += Math.min((height - waterLevel) / 10, 1) * (ridgedNoise.get(x, 0, z) - 0.5) * 8;
        }
        return height;
    }

    private Biome getBlendedBiome(double x, double z) {
        double biomeValue = getBiomeAt(x, z);

        int biomeIndex = Math.floorMod((int) (biomeValue * biomes.size()), biomes.size());
        return biomes.get(Math.min(biomeIndex, biomes.size() - 1));
    }

    public double getBiomeAt(double worldX, double worldZ) {
        // Calculate wobble displacement
        double wobble = biomeWobbleNoise.get(worldX, 0d, worldZ);
        double rotation = wobble / biomeWobbleNoise.maxValue() * Math.PI * 2;

        double displacementX = Math.sin(rotation);
        double displacementZ = Math.cos(rotation);

        double distortion = biomeDistortNoise.get(worldX, 0, worldZ) / biomeDistortNoise.maxValue() * 5;

        return biomeCellNoise.get(worldX + displacementX * 5, distortion, worldZ + displacementZ * 5);
    }
}