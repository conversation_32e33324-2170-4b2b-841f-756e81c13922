package dev.debutter.weirdlycubed.blocks.types;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.blocks.listeners.BreakBlockHandler;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.Random;

public class CattailStalk extends CustomBlock {

    private static final Random dropRng = new Random();

    public CattailStalk() {
        super(Block.GREEN_STAINED_GLASS_PANE.withProperty("waterlogged", "true"), "cattail_stalk");
    }

    @NotNull
    @Override
    public Sound getBreakSound() {
        return Sound.sound(Key.key("block.big_dripleaf.break"), Sound.Source.BLOCK, 1f, 1f);
    }

    @Override
    public int getMiningDuration() {
        return 3;
    }

    @Override
    public ItemStack[] getItemDrops(@NotNull Destroy destroy) {
        if (dropRng.nextBoolean()) return new ItemStack[0];

        return new ItemStack[] { CustomItemTypes.CATTAIL.getItemStack() };
    }

    @Override
    public void onBreak(@NotNull Destroy destroy) {
        super.onBreak(destroy);

        Instance instance = destroy.getInstance();
        Point point = destroy.getBlockPosition();
        int y = point.blockY() - 1;

        // Destroy all cattail stalks going down
        while (true) {
            Point iterPoint = point.withY(y);
            Block iterBlock = instance.getBlock(iterPoint);
            if (!(iterBlock.handler() instanceof CattailStalk)) break;

            BreakBlockHandler.destroyBlock(new InstanceLoc(instance, iterPoint));

            y -= 1;
        }

        // Destroy all cattail stalks and stem going up
        y = point.blockY() + 1;

        while (true) {
            Point iterPoint = point.withY(y);
            Block iterBlock = instance.getBlock(iterPoint);
            if (!(iterBlock.handler() instanceof CattailStalk || iterBlock.handler() instanceof Cattail)) break;

            BreakBlockHandler.destroyBlock(new InstanceLoc(instance, iterPoint));

            y += 1;
        }
    }
}
