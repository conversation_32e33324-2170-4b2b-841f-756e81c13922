package dev.debutter.weirdlycubed.instances;

import dev.debutter.weirdlycubed.Main;
import dev.debutter.weirdlycubed.instances.generators.NormalishGen;
import dev.debutter.weirdlycubed.instances.generators.WorldGen;
import dev.debutter.weirdlycubed.server.GameSaver;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import dev.debutter.weirdlycubed.utils.PersistentStorage;
import net.minestom.server.MinecraftServer;
import net.minestom.server.event.instance.InstanceChunkUnloadEvent;
import net.minestom.server.instance.InstanceContainer;
import net.minestom.server.instance.InstanceManager;
import net.minestom.server.instance.anvil.AnvilLoader;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.UnknownNullability;

import java.io.File;
import java.nio.file.Path;
import java.util.Random;
import java.util.concurrent.ExecutionException;

public class WorldManager {

    private static InstanceContainer instanceContainer;
    private static PersistentStorage levelStorage = new PersistentStorage(Path.of("world"), "level");
    private static Tag<Long> seedTag = Tag.Long("seed");

    public static void init() {
        InstanceManager instanceManager = MinecraftServer.getInstanceManager();

        // Generate world with seed
        @UnknownNullability Long seedData = levelStorage.read().getTag(seedTag);
        long seed = seedData == null ? new Random().nextLong() : seedData;

        WorldGen worldGen = new NormalishGen(seed);
        levelStorage.read().setTag(seedTag, seed);

        // Set up the instance container with the selected generator
        instanceContainer = instanceManager.createInstanceContainer();
        instanceContainer.setChunkSupplier(worldGen);
        instanceContainer.setGenerator(worldGen);

        // Load a pre-existing world if a path is provided or if the default "world" folder exists
        String worldPath = Main.getConfig().getString("world.path");
        if (doWorldsSave()) {
            File worldFolder = new File(worldPath);
            if (!worldFolder.exists()) worldFolder.mkdirs();

            instanceContainer.setChunkLoader(new AnvilLoader(worldPath));
        }
    }

    public static InstanceContainer getInstanceContainer() {
        return instanceContainer;
    }

    public static String getWorldPath() {
        return Main.getConfig().getString("world.path");
    }

    public static boolean doWorldsSave() {
        return GameSaver.doSaving() && !getWorldPath().isEmpty();
    }

    public static void saveWorld() {
        if (!doWorldsSave()) return;

        ConsoleLogger.info("Saving the world...");

        try {
            instanceContainer.saveChunksToStorage().get(); // Block the current thread until the instance is saved
            // TODO: When there are multiple instances, try saving them in parallel

            // Save the level data
            levelStorage.save();

            ConsoleLogger.info("World successfully saved.");
        } catch (InterruptedException | ExecutionException e) {
            ConsoleLogger.exception("Failed to save the world", e);
        }
    }

    public static void onChunkUnload(@NotNull InstanceChunkUnloadEvent event) {
        instanceContainer.saveChunkToStorage(event.getChunk());
    }
}
