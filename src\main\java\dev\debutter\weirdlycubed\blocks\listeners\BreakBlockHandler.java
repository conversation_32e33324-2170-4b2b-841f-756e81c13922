package dev.debutter.weirdlycubed.blocks.listeners;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.utils.Garden;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import net.minestom.server.coordinate.BlockVec;
import net.minestom.server.entity.Player;
import net.minestom.server.event.player.PlayerBlockBreakEvent;
import net.minestom.server.event.player.PlayerCancelDiggingEvent;
import net.minestom.server.event.player.PlayerStartDiggingEvent;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.network.packet.server.play.BlockBreakAnimationPacket;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class BreakBlockHandler {

    private static final float MINING_REPAIR_SPEED = 0.0625f;
    /** UUIDs of players mapped to block positions, represents what block a player is currently mining */
    private static final HashMap<UUID, InstanceLoc> playerMining = new HashMap<>();
    /** Total amount of time spent mining a position */
    private static final ConcurrentHashMap<InstanceLoc, Float> miningProgress = new ConcurrentHashMap<>();

    public static void tickMining(Player player) {
        UUID uuid = player.getUuid();
        @Nullable InstanceLoc instanceLoc = playerMining.get(uuid);
        if (instanceLoc == null) return;

        @Nullable BlockHandler blockHandler = instanceLoc.block().handler();
        if (!(blockHandler instanceof CustomBlock customBlock)) return;

        int ticksUntilMined = customBlock.getMiningDuration();
        float miningTime = miningProgress.getOrDefault(instanceLoc, 0f);
        float miningSpeed = getMiningSpeed(player);

        // Mining has finished
        if (miningTime >= ticksUntilMined || miningSpeed >= ticksUntilMined) {
            destroyBlock(instanceLoc);
            miningProgress.remove(instanceLoc);
            sendBlockDamage(instanceLoc, (byte) 10);
            return;
        }

        // Not done mining
        byte progress = (byte) (10 * miningTime / ticksUntilMined);
        miningProgress.put(instanceLoc, miningTime + miningSpeed);
        sendBlockDamage(instanceLoc, progress);
    }

    /** Repairs blocks that are currently not being mined */
    public static void tickRepair() {
        Collection<InstanceLoc> currentlyMining = playerMining.values();

        for (InstanceLoc partiallyMined : miningProgress.keySet()) {
            // Skip if the block is currently being mined
            if (currentlyMining.contains(partiallyMined)) continue;

            // Get custom block
            @Nullable BlockHandler blockHandler = partiallyMined.block().handler();
            if (!(blockHandler instanceof CustomBlock customBlock)) return;

            int ticksUntilMined = customBlock.getMiningDuration();

            // Slowly decrease mining progress
            float miningTime = Math.max(0, miningProgress.get(partiallyMined) - MINING_REPAIR_SPEED);
            if (miningTime == 0) {
                miningProgress.remove(partiallyMined);

                sendBlockDamage(partiallyMined, (byte) 10);
            } else {
                miningProgress.put(partiallyMined, miningTime);

                byte progress = (byte) Math.min(9, 10 * miningTime / ticksUntilMined);
                sendBlockDamage(partiallyMined, progress);
            }
        }
    }

    /** @return A value between 0 and 1; 0 meaning unable to mine and 1 meaning perfect mining speed */
    private static float getMiningSpeed(Player player) {
        float baseSpeed = switch (PlayerRuntimeData.getPlayerMode(player)) {
            case SURVIVAL -> 1.0f;
            case GOD -> 1_000_000_000.0f;
            case GHOST -> 0.0f;
        };

        // TODO: if the player is underwater, instead add 0.5 for slower mining

        return baseSpeed;
    }

    public static void destroyBlock(InstanceLoc loc) {
        loc.instance().setBlock(loc.point(), Garden.isWaterLogged(loc.block()) ? Block.WATER : Block.AIR);
    }

    private static void sendBlockDamage(InstanceLoc instanceLoc, byte progress) {
        BlockBreakAnimationPacket packet = new BlockBreakAnimationPacket(instanceLoc.hashCode(), instanceLoc.point(), progress);

        // TODO: Don't send to players very far away
        for (Player player : instanceLoc.instance().getPlayers()) {
            player.getPlayerConnection().sendPacket(packet);
        }
    }

    public static void onPlayerStartDigging(@NotNull PlayerStartDiggingEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUuid();
        Instance instance = event.getInstance();
        BlockVec blockPos = event.getBlockPosition();

        playerMining.put(uuid, new InstanceLoc(instance, blockPos));
    }

    public static void onPlayerStopDigging(@NotNull PlayerCancelDiggingEvent event) {
        UUID uuid = event.getPlayer().getUuid();
        playerMining.remove(uuid);
    }

    /// Fixes custom mining for natively instant break blocks
    public static void onPlayerBlockBreak(@NotNull PlayerBlockBreakEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUuid();
        Instance instance = event.getInstance();
        BlockVec blockPos = event.getBlockPosition();
        InstanceLoc instanceLoc = new InstanceLoc(instance, blockPos);

        // Get the mining duration of the custom block
        @Nullable BlockHandler blockHandler = instanceLoc.block().handler();
        if (!(blockHandler instanceof CustomBlock customBlock)) {
            event.setCancelled(true);
            return;
        }

        int ticksUntilMined = customBlock.getMiningDuration();
        @Nullable Float miningTime = miningProgress.get(instanceLoc);
        float miningSpeed = getMiningSpeed(player);

        if ((miningTime == null || miningTime < ticksUntilMined) && miningSpeed < ticksUntilMined) {
            playerMining.put(uuid, instanceLoc);
            event.setCancelled(true);
        }
    }
}
