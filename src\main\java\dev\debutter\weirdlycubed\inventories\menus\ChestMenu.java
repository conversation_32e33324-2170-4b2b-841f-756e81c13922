package dev.debutter.weirdlycubed.inventories.menus;

import dev.debutter.weirdlycubed.blocks.types.Chest;
import dev.debutter.weirdlycubed.inventories.InvMenu;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import dev.debutter.weirdlycubed.utils.objects.ContainerItem;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.kyori.adventure.text.Component;
import net.minestom.server.entity.Player;
import net.minestom.server.event.inventory.*;
import net.minestom.server.instance.Instance;
import net.minestom.server.inventory.InventoryType;
import net.minestom.server.item.ItemStack;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class ChestMenu extends InvMenu {

    private static final Tag<List<ContainerItem>> contentsTag = Tag.Structure("contents", ContainerItem.class).list();
    private final @NotNull InstanceLoc chestLoc;

    public ChestMenu(@NotNull InventoryType inventoryType, @NotNull Component title, @NotNull InstanceLoc loc) {
        super(inventoryType, title);

        this.chestLoc = loc;
    }

    @Override
    protected void onClose(@NotNull InventoryCloseEvent event) {
        Player player = event.getPlayer();
        Instance instance = player.getInstance();

        Sound chestCloseSound = Sound.sound(Key.key("block.chest.close"), Sound.Source.BLOCK, 1f, 1f);
        player.playSound(chestCloseSound, chestLoc.blockCenter());

        instance.sendBlockAction(chestLoc.point(), (byte) 0, (byte) 1);
        instance.sendBlockAction(chestLoc.point(), (byte) 1, (byte) 0);

        // The current player was the last to close the inventory, so save to nbt
        if (this.viewers.size() == 1 && this.isViewer(player)) {
            Chest.saveToNBT(chestLoc, this);
        }
    }

    @Override
    protected void onClick(@NotNull InventoryClickEvent event) {

    }

    @Override
    protected void onOpen(@NotNull InventoryOpenEvent event) {
        Player player = event.getPlayer();
        Instance instance = player.getInstance();

        Sound chestOpenSound = Sound.sound(Key.key("block.chest.open"), Sound.Source.BLOCK, 1f, 1f);
        instance.playSound(chestOpenSound, chestLoc.blockCenter());

        player.getInstance().sendBlockAction(chestLoc.point(), (byte) 1, (byte) 1);

        // The current player was the first to open the inventory, so load from nbt
        if (this.viewers.isEmpty()) {
            for (ContainerItem containerItem : Chest.loadFromNBT(chestLoc.block())) {
                int slot = containerItem.slot();
                ItemStack item = containerItem.item();

                this.setItemStack(slot, item);
            }
        }
    }

    @Override
    protected void onPreClick(@NotNull InventoryPreClickEvent event) {}

    @Override
    protected void onItemChange(@NotNull InventoryItemChangeEvent event) {}

}
