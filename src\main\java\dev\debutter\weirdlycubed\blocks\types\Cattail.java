package dev.debutter.weirdlycubed.blocks.types;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import dev.debutter.weirdlycubed.blocks.listeners.BreakBlockHandler;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.Garden;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.item.ItemStack;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.UnknownNullability;

import java.util.Random;

public class Cattail extends CustomBlock {

    private static final @NotNull Tag<Boolean> maxGrowthTag = Tag.Boolean("max_growth");
    private static final Random growthRng = new Random();

    public Cattail() {
        super(Block.BROWN_CANDLE.withProperty("waterlogged", "true"), "cattail");
    }

    @NotNull
    @Override
    public Sound getBreakSound() {
        return Sound.sound(Key.key("block.big_dripleaf.break"), Sound.Source.BLOCK, 1f, 1f);
    }

    @Override
    public int getMiningDuration() {
        return 3;
    }

    @Override
    public ItemStack[] getItemDrops(@NotNull Destroy destroy) {
        return new ItemStack[] { CustomItemTypes.CATTAIL.getItemStack() };
    }

    @Override
    public void onBreak(@NotNull Destroy destroy) {
        super.onBreak(destroy);

        Instance instance = destroy.getInstance();
        Point point = destroy.getBlockPosition();
        int y = point.blockY() - 1;

        // Destroy all cattail stalks going down
        while (true) {
            Point iterPoint = point.withY(y);
            Block iterBlock = instance.getBlock(iterPoint);
            if (!(iterBlock.handler() instanceof CattailStalk)) break;

            BreakBlockHandler.destroyBlock(new InstanceLoc(instance, iterPoint));

            y -= 1;
        }
    }

    @Override
    public void randomTick(@NotNull Tick tick) {
        Instance instance = tick.getInstance();
        Block block = tick.getBlock();
        Point point = tick.getBlockPosition();
        Point pointAbove = point.withY(point.y() + 1);
        @UnknownNullability Boolean atMaxGrowth = block.getTag(maxGrowthTag);

        // Cancel random tick if at max growth
        if (atMaxGrowth != null && atMaxGrowth) return;

        // Place the stalk
        Block stalk = CustomBlockTypes.CATTAIL_STALK.getBlock()
            .withProperty("waterlogged", Garden.isWaterLogged(block) ? "true" : "false");

        instance.setBlock(point, stalk);

        // Place the stem
        boolean waterlogged = Garden.isWaterLogged(instance.getBlock(pointAbove));
        Block stem = CustomBlockTypes.CATTAIL.getBlock()
            .withProperty("waterlogged", waterlogged ? "true" : "false");

        // Decide whether to stop growing when above water
        if (!waterlogged && growthRng.nextBoolean()) {
            stem = stem.withTag(maxGrowthTag, true);
        }

        instance.setBlock(pointAbove, stem);
    }

    @Override
    public boolean isReplaceableWith(CustomBlock customBlock) {
        return super.isReplaceableWith(customBlock) || customBlock instanceof CattailStalk;
    }

    @Override
    public boolean isRandomlyTickable() {
        return true;
    }

    @Override
    public boolean canPlace(Instance instance, Point position) {
        return instance.getBlock(position).id() == Block.WATER.id();
    }
}
