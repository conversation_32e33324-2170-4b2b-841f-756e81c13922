package dev.debutter.weirdlycubed.commands;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.command.builder.arguments.ArgumentWord;
import net.minestom.server.entity.Player;

public class TpCmd extends Command {
    public TpCmd() {
        super("tp");

        setDefaultExecutor((sender, context) ->
            sender.sendMessage(Component.text("Usage: /tp <player1> [<player2>]", NamedTextColor.GRAY))
        );

        ArgumentWord player1Argument = ArgumentType.Word("player1");
        player1Argument.setCallback((sender, exception) ->
            sender.sendMessage(Component.text("Player 1 not found!", NamedTextColor.RED))
        );

        ArgumentWord player2Argument = ArgumentType.Word("player2");
        player2Argument.setCallback((sender, exception) ->
            sender.sendMessage(Component.text("Player 2 not found!", NamedTextColor.RED))
        );

        addSyntax((sender, context) -> {
            String player1Name = context.get(player1Argument);
            String player2Name = context.get(player2Argument);

            Player player1 = getPlayerByName(player1Name);
            Player player2 = player2Name != null ? getPlayerByName(player2Name) : null;

            if (player1 == null) {
                sender.sendMessage(Component.text("Player " + player1Name + " not found!", NamedTextColor.RED));
                return;
            }

            if (player2 == null && sender instanceof Player) {
                player2 = (Player) sender;
                if (player1.equals(player2)) {
                    sender.sendMessage(Component.text("You cannot teleport to yourself!", NamedTextColor.RED));
                    return;
                }
            }

            if (player2 == null) {
                sender.sendMessage(Component.text("Please specify a valid target player!", NamedTextColor.RED));
                return;
            }

            if (!player1.getInstance().equals(player2.getInstance())) {
                sender.sendMessage(Component.text("Players are not in the same instance!", NamedTextColor.RED));
                return;
            }

            // Teleport player1 to player2's position
            player1.teleport(player2.getPosition());
            sender.sendMessage(Component.text("Teleported " + player1.getUsername() + " to " + player2.getUsername(), NamedTextColor.GREEN));
        }, player1Argument, player2Argument);
    }

    private Player getPlayerByName(String playerName) {
        for (Player player : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
            if (player.getUsername().equalsIgnoreCase(playerName)) {
                return player;
            }
        }
        return null;
    }
}
