package dev.debutter.weirdlycubed.player.data.serializer;

import dev.debutter.weirdlycubed.player.data.GenericSerializer;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.nbt.ListBinaryTag;
import net.minestom.server.coordinate.Vec;
import net.minestom.server.entity.Player;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class VelocitySerializer extends GenericSerializer<List<Double>> {

    @Override
    public void applyTo(@NotNull CompoundBinaryTag data, Player player) {
        ListBinaryTag elements = data.getList(getTag().getKey());
        if (elements.isEmpty()) return; // Cancel if data is not defined

        double x = elements.getDouble(0);
        double y = elements.getDouble(1);
        double z = elements.getDouble(2);

        player.setVelocity(new Vec(
            x,
            y,
            z
        ));
    }

    @NotNull
    @Override
    public Tag<List<Double>> getTag() {
        return Tag.Double("velocity").list();
    }

    @NotNull
    @Override
    public List<Double> serialize(Player player) {
        Vec velocity = player.getVelocity();

        return List.of(
            velocity.x(),
            velocity.y(),
            velocity.z()
        );
    }
}
