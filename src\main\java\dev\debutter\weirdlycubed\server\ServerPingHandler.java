package dev.debutter.weirdlycubed.server;

import dev.debutter.weirdlycubed.Main;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import dev.dejvokep.boostedyaml.YamlDocument;
import net.minestom.server.event.server.ServerListPingEvent;
import net.minestom.server.ping.ResponseData;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;

public class ServerPingHandler {

    public static void onServerPing(@NotNull ServerListPingEvent event) {
        ResponseData resData = event.getResponseData();
        YamlDocument config = Main.getConfig();
        String motd = config.getString("server-list.motd");
        String iconPath = config.getString("server-list.icon-path");

        resData.setDescription(AwesomerText.beautifyMessage(motd));

        if (iconPath != null && !iconPath.isEmpty()) {
            File iconFile = new File(iconPath);

            if (iconFile.exists() && iconFile.isFile()) {
                try {
                    byte[] fileContent = Files.readAllBytes(iconFile.toPath());
                    String encodedString = Base64.getEncoder().encodeToString(fileContent);
                    resData.setFavicon("data:image/png;base64," + encodedString);
                } catch (IOException e) {
                    ConsoleLogger.exception(e);
                }
            }
        }

        event.setResponseData(resData);
    }
}
