package dev.debutter.weirdlycubed.player;

import dev.debutter.weirdlycubed.crafting.CraftingManager;
import dev.debutter.weirdlycubed.crafting.matrix.CraftingMatrix;
import dev.debutter.weirdlycubed.crafting.matrix.Slot;
import dev.debutter.weirdlycubed.crafting.recipe.CraftingRecipe;
import dev.debutter.weirdlycubed.utils.Garden;
import net.minestom.server.entity.Player;
import net.minestom.server.event.inventory.InventoryItemChangeEvent;
import net.minestom.server.event.inventory.InventoryPreClickEvent;
import net.minestom.server.inventory.PlayerInventory;
import net.minestom.server.item.ItemStack;
import net.minestom.server.utils.inventory.PlayerInventoryUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class InventoryHandler {

    public static void onInventoryItemChange(@NotNull InventoryItemChangeEvent event) {
        if (!(event.getInventory() instanceof PlayerInventory inventory)) return;

        if (!(
            event.getSlot() == PlayerInventoryUtils.CRAFT_RESULT ||
            event.getSlot() == PlayerInventoryUtils.CRAFT_SLOT_1 ||
            event.getSlot() == PlayerInventoryUtils.CRAFT_SLOT_2 ||
            event.getSlot() == PlayerInventoryUtils.CRAFT_SLOT_3 ||
            event.getSlot() == PlayerInventoryUtils.CRAFT_SLOT_4)) return;

        CraftingMatrix matrix = new CraftingMatrix()
            .setSlot(Slot.TOP_LEFT, inventory.getItemStack(PlayerInventoryUtils.CRAFT_SLOT_1))
            .setSlot(Slot.TOP_MIDDLE, inventory.getItemStack(PlayerInventoryUtils.CRAFT_SLOT_2))
            .setSlot(Slot.MIDDLE_LEFT, inventory.getItemStack(PlayerInventoryUtils.CRAFT_SLOT_3))
            .setSlot(Slot.MIDDLE_MIDDLE, inventory.getItemStack(PlayerInventoryUtils.CRAFT_SLOT_4));

        @Nullable CraftingRecipe recipe = CraftingManager.match(matrix);

        if (recipe == null) {
            inventory.setItemStack(PlayerInventoryUtils.CRAFT_RESULT, ItemStack.AIR);
        } else {
            inventory.setItemStack(PlayerInventoryUtils.CRAFT_RESULT, recipe.output());
        }

        inventory.update();
    }

    public static void onInventoryClick(@NotNull InventoryPreClickEvent event) {
        // Handle just clicking the crafting output slot
        if (event.getSlot() != PlayerInventoryUtils.CRAFT_RESULT) return;

        PlayerInventory inventory = event.getPlayer().getInventory();
        ItemStack outputItem = inventory.getItemStack(PlayerInventoryUtils.CRAFT_RESULT);
        ItemStack cursorItem = event.getPlayer().getInventory().getCursorItem();

        // TODO: properly handle shift clicking, dropping, and number keys

        // Cancel if there is nothing in the output slot to prevent putting items there
        if (outputItem.equals(ItemStack.AIR)) {
            event.setCancelled(true);
            return;
        }

        // Check if the output item and cursor item can be merged
        if (!cursorItem.equals(ItemStack.AIR)) {
            if (outputItem.isSimilar(cursorItem)) {
                int currentAmount = cursorItem.amount();

                if (currentAmount < outputItem.maxStackSize()) {
                    inventory.setCursorItem(outputItem.withAmount(currentAmount + 1));
                    event.setCancelled(true);
                } else {
                    event.setCancelled(true);
                    return;
                }
            } else {
                event.setCancelled(true);
                return;
            }
        } else {
            inventory.setCursorItem(outputItem);
            event.setCancelled(true);
        }

        // Use items
        for (int slot : new int[] {
            PlayerInventoryUtils.CRAFT_SLOT_1,
            PlayerInventoryUtils.CRAFT_SLOT_2,
            PlayerInventoryUtils.CRAFT_SLOT_3,
            PlayerInventoryUtils.CRAFT_SLOT_4
        }) {
            ItemStack item = inventory.getItemStack(slot);
            int amount = item.amount();

            if (amount <= 1) {
                inventory.setItemStack(slot, ItemStack.AIR);
            } else {
                inventory.setItemStack(slot, item.withAmount(amount - 1));
            }
        }

        // Update the players inventory
        inventory.update();
    }

    public static void onInventoryClose(@NotNull Player player) {
        PlayerInventory inventory = player.getInventory();

        for (int slot : new int[] {
            PlayerInventoryUtils.CRAFT_SLOT_1,
            PlayerInventoryUtils.CRAFT_SLOT_2,
            PlayerInventoryUtils.CRAFT_SLOT_3,
            PlayerInventoryUtils.CRAFT_SLOT_4
        }) {
            ItemStack item = inventory.getItemStack(slot);
            inventory.setItemStack(slot, ItemStack.AIR);

            // Skip if item was added successfully
            if (inventory.addItemStack(item)) continue;

            // Drop item on the ground since the item couldn't be added to the players inventory
            Garden.spawnItemEntity(player.getInstance(), player.getPosition(), item);
        }

        // Clear the output slot
        inventory.setItemStack(PlayerInventoryUtils.CRAFT_RESULT, ItemStack.AIR);

        // Update the players inventory
        inventory.update();
    }

}
