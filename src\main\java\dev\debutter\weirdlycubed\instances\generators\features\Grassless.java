package dev.debutter.weirdlycubed.instances.generators.features;

import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.generator.GenerationUnit;

import java.util.Objects;
import java.util.Random;

public class Grassless extends Biome {

    private final long foliageSeed;

    public Grassless(long seed) {
        super(seed);

        Random rng = new Random(seed);

        this.foliageSeed = rng.nextLong();
    }

    @Override
    public void generateTerrain(GenerationUnit unit, Point bottom, int stoneHeight, int waterLevel, boolean favorLower) {
        for (int y = 0; y <= stoneHeight; y++) {
            unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.STONE.getBlock());
        }

        int dirtLayers = 3;

        for (int layer = 0; layer < dirtLayers; layer++) {
            int dirtBase = stoneHeight + 1 + layer * 2;

            for (int y = dirtBase; y < dirtBase + 2; y++) {
                unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.DIRT.getBlock());
            }
        }

        int grassBase = stoneHeight + 1 + dirtLayers * 2;
        if (grassBase > waterLevel) {
            if (grassBase == waterLevel + 1 && favorLower) {
                unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.SAND.getBlock());
            } else {
                unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.DIRT.getBlock());
            }
        } else {
            unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.SAND.getBlock());

            int seaFloor = grassBase + 1;

            // Randomly place cattails
            int cattailDiff = seaFloor - waterLevel;
            if (cattailDiff > -3 && cattailDiff <= 0) {
                boolean placeCattail = new Random(Objects.hash(foliageSeed, bottom.x(), seaFloor, bottom.z())).nextInt(switch (cattailDiff) {
                    case -2 -> 50;
                    case -1 -> 12;
                    case 0 -> 4;
                    default -> throw new IllegalStateException("Unexpected value: " + cattailDiff);
                }) == 0;

                if (placeCattail) {
                    unit.modifier().setBlock(bottom.withY(seaFloor), CustomBlockTypes.CATTAIL.getBlock());

                    seaFloor += 1;
                }
            }

            // Fill the sea floor with water
            for (int y = seaFloor; y <= waterLevel; y++) {
                unit.modifier().setBlock(bottom.withY(y), Block.WATER);
            }
        }
    }
}
