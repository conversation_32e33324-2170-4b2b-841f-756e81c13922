package dev.debutter.weirdlycubed.server;

import dev.debutter.weirdlycubed.Constants;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import me.lucko.spark.api.Spark;
import me.lucko.spark.api.SparkProvider;
import me.lucko.spark.api.statistic.StatisticWindow;
import me.lucko.spark.api.statistic.misc.DoubleAverageInfo;
import me.lucko.spark.api.statistic.types.GenericStatistic;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;
import net.minestom.server.entity.Player;
import net.minestom.server.network.packet.server.play.PlayerListHeaderAndFooterPacket;
import org.jetbrains.annotations.Nullable;

import java.text.DecimalFormat;

public class CustomTabList {

    public static void sendHeaderAndFooter(Player player, TextComponent header, TextComponent footer) {
        PlayerListHeaderAndFooterPacket packet = new PlayerListHeaderAndFooterPacket(header, footer);
        player.getPlayerConnection().sendPacket(packet);
    }

    public static void updateTabList(Player player) {
        Component header = AwesomerText.beautifyMessage(
                "<gradient:gold:yellow:gold> <project_name> v<version> </gradient>",
                Placeholder.unparsed("project_name", Constants.PROJECT_NAME),
                Placeholder.unparsed("version", Constants.VERSION)
        );
        Component footer = CustomTabList.getServerInfoComponent();
        CustomTabList.sendHeaderAndFooter(player, (TextComponent) header, (TextComponent) footer);
    }

    public static Component getServerInfoComponent() {
        // Get statistics from spark
        Spark spark = SparkProvider.get();

        // Get cpu usage in the last minute
        double cpuUsage = spark.cpuProcess().poll(StatisticWindow.CpuUsage.SECONDS_10);

        // Get mspt in the last minute
        @Nullable GenericStatistic<DoubleAverageInfo, StatisticWindow.MillisPerTick> msptStat = spark.mspt();

        double mspt = msptStat == null ? -1 : msptStat.poll(StatisticWindow.MillisPerTick.SECONDS_10).mean();

        // Get java runtime memory usage
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / maxMemory * 100;

        // Format the tab list message
        DecimalFormat decimalFormat = new DecimalFormat("0.00");

        return AwesomerText.beautifyMessage("<newline><gray>CPU Usage: <cpu_usage>" +
                        "<newline>Memory Used: <memory_usage>" +
                        "<newline>MSPT: <mspt>",
                Placeholder.unparsed("cpu_usage", decimalFormat.format(cpuUsage) + "%"),
                Placeholder.unparsed("memory_usage", decimalFormat.format(memoryUsage) + "%"),
                Placeholder.unparsed("mspt", mspt == -1 ? "???" : decimalFormat.format(mspt))
        );
    }
}