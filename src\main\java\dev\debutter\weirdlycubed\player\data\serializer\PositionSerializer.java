package dev.debutter.weirdlycubed.player.data.serializer;

import dev.debutter.weirdlycubed.player.data.GenericSerializer;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.nbt.ListBinaryTag;
import net.minestom.server.coordinate.Pos;
import net.minestom.server.entity.Player;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class PositionSerializer extends GenericSerializer<List<Double>> {

    @Override
    public void applyTo(@NotNull CompoundBinaryTag data, Player player) {
        ListBinaryTag elements = data.getList(getTag().getKey());
        if (elements.isEmpty()) return; // Cancel if data is not defined

        Pos position = player.getPosition();

        double x = elements.getDouble(0);
        double y = elements.getDouble(1);
        double z = elements.getDouble(2);

        player.teleport(new Pos(
            x,
            y,
            z,
            position.yaw(),
            position.pitch()
        ));
    }

    @NotNull
    @Override
    public Tag<List<Double>> getTag() {
        return Tag.Double("position").list();
    }

    @NotNull
    @Override
    public List<Double> serialize(Player player) {
        Pos position = player.getPosition();

        return List.of(
            position.x(),
            position.y(),
            position.z()
        );
    }
}
