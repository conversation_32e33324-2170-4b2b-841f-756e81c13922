package dev.debutter.weirdlycubed.player.data;

import dev.debutter.weirdlycubed.player.data.serializer.InventorySerializer;
import dev.debutter.weirdlycubed.player.data.serializer.PositionSerializer;
import dev.debutter.weirdlycubed.player.data.serializer.RotationSerializer;
import dev.debutter.weirdlycubed.player.data.serializer.VelocitySerializer;
import dev.debutter.weirdlycubed.utils.PersistentStorage;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.minestom.server.entity.Player;
import net.minestom.server.tag.TagHandler;
import org.jetbrains.annotations.NotNull;

import java.nio.file.Path;
import java.util.List;

public class PlayerStorage {

    private static final List<GenericSerializer<?>> serializers = List.of(
        new InventorySerializer(),
        new PositionSerializer(),
        new RotationSerializer(),
        new VelocitySerializer()
    );

    /**
     * Saves the new data of a player to a file
     */
    public static boolean savePlayerData(Player player) {
        // Write the player data
        writePlayerData(player);

        // Save the player data
        return getPlayerStorage(player).save();
    }

    /**
     * Loads and applies previously stored player data
     */
    public static void loadPlayerData(Player player) {
        PersistentStorage playerStorage = getPlayerStorage(player);
        @NotNull CompoundBinaryTag nbt = playerStorage.read().asCompound();

        // Apply the saved player data to the player
        for (GenericSerializer<?> serializer : serializers) {
            serializer.applyTo(nbt, player);
        }
    }

    private static PersistentStorage getPlayerStorage(Player player) {
        return new PersistentStorage(Path.of("players"), player.getUuid().toString());
    }

    /**
     * Writes the data of the player onto the persistent data storage
     */
    private static void writePlayerData(Player player) {
        TagHandler handler = getPlayerStorage(player).read();

        // Use every available serializer to collect the players data
        for (GenericSerializer<?> serializer : serializers) {
            serializer.writeTo(handler, player);
        }
    }

}
