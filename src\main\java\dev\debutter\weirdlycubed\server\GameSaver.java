package dev.debutter.weirdlycubed.server;

import dev.debutter.weirdlycubed.Main;
import dev.debutter.weirdlycubed.instances.WorldManager;
import dev.debutter.weirdlycubed.utils.PersistentStorage;

public class GameSaver {

    private static long ticksSinceLastSave = 0;

    public static void tick() {
        // Check if saving is enabled
        if (!doSaving()) return;

        // Check whether to trigger an autosave
        if (ticksSinceLastSave >= Main.getConfig().getLong("saving.interval")) {
            ticksSinceLastSave = 0;

            save();
            return;
        }

        // Increment the autosave timer
        ticksSinceLastSave += 1;
    }

    public static void save() {
        WorldManager.saveWorld();

        // Flush persistent data
        PersistentStorage.flushAll();
    }

    public static boolean doSaving() {
        return Main.getConfig().getBoolean("saving.enabled");
    }

}
