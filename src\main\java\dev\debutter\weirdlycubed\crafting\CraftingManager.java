package dev.debutter.weirdlycubed.crafting;

import dev.debutter.weirdlycubed.crafting.matrix.CraftingMatrix;
import dev.debutter.weirdlycubed.crafting.recipe.CraftingRecipe;
import dev.debutter.weirdlycubed.crafting.recipe.ShapedRecipe;
import dev.debutter.weirdlycubed.crafting.recipe.ShapelessRecipe;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;

public class CraftingManager {

    private static final ArrayList<CraftingRecipe> recipes = new ArrayList<>();

    // TODO: send recipe book packets to players or something

    static {
        // Register all the custom crafting recipes
        recipes.add(new ShapelessRecipe(
            CustomItemTypes.TWISTED_GRASS,
            CustomItemTypes.GRASS_BLADES,
            CustomItemTypes.GRASS_BLADES
        ));
        recipes.add(new ShapelessRecipe(
            CustomItemTypes.SHARP_ROCK,
            CustomItemTypes.ROCK,
            CustomItemTypes.ROCK
        ));
//        recipes.add(new ShapedRecipe(
//            CustomItemTypes.STONE,
//            new ReferenceMatrix()
//                .setSlot(Slot.TOP_LEFT, CustomItemTypes.SAND)
//                .setSlot(Slot.MIDDLE_MIDDLE, CustomItemTypes.DIRT)
//        ));
    }

    public static @Nullable CraftingRecipe match(CraftingMatrix matrix) { // TODO: add crafting sessions
        ArrayList<CraftingMatrix> combinations = matrix.jostle();

        for (CraftingRecipe recipe : recipes) {
            if (recipe instanceof ShapedRecipe) {
                // Loop over every combination if the recipe is shaped
                for (CraftingMatrix combination : combinations) {
                    if (recipe.match(combination)) {
                        return recipe;
                    }
                }
            } else {
                // Otherwise the recipe is shapeless so simply match with the original matrix
                if (recipe.match(matrix)) {
                    return recipe;
                }
            }

        }

        return null;
    }

}
