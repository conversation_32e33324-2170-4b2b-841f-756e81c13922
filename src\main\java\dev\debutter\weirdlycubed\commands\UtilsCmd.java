package dev.debutter.weirdlycubed.commands;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.ArgumentString;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.command.builder.arguments.ArgumentWord;
import net.minestom.server.command.builder.arguments.number.ArgumentInteger;
import net.minestom.server.entity.Player;

public class UtilsCmd extends Command {

    public UtilsCmd() {
        super("utils");

        setDefaultExecutor((sender, context) -> sender.sendMessage(Component.text("Usage: /utils <command>", NamedTextColor.GRAY)));

        addSubcommand(new RandNumCmd());
        addSubcommand(new FactorialCmd());
        addSubcommand(new CmdCmd());
    }

    static class RandNumCmd extends Command {

        public RandNumCmd() {
            super("rand");

            setDefaultExecutor((sender, context) -> sender.sendMessage(Component.text("Usage: /utils rand <min> <max>", NamedTextColor.GRAY)));

            ArgumentInteger minArgument = ArgumentType.Integer("min");
            ArgumentInteger maxArgument = ArgumentType.Integer("max");

            minArgument.setCallback((sender, exception) -> {
                final String input = exception.getInput();
                sender.sendMessage(Component.text("The value " + input + " is invalid for minimum range!", NamedTextColor.RED));
            });

            maxArgument.setCallback((sender, exception) -> {
                final String input = exception.getInput();
                sender.sendMessage(Component.text("The value " + input + " is invalid for maximum range!", NamedTextColor.RED));
            });

            addSyntax((sender, context) -> {
                final int min = context.get(minArgument);
                final int max = context.get(maxArgument);

                if (min >= max) {
                    sender.sendMessage(Component.text("Minimum value must be less than maximum value!", NamedTextColor.RED));
                    return;
                }

                final int randomNumber = (int) (Math.random() * (max - min + 1)) + min;
                sender.sendMessage(Component.text("Your random number between " + min + " and " + max + " is: " + randomNumber, NamedTextColor.GREEN));
            }, minArgument, maxArgument);
        }
    }

    static class FactorialCmd extends Command {

        public FactorialCmd() {
            super("fact");

            setDefaultExecutor((sender, context) -> sender.sendMessage(Component.text("Usage: /utils fact <number>", NamedTextColor.GRAY)));

            ArgumentInteger numberArgument = ArgumentType.Integer("number");

            numberArgument.setCallback((sender, exception) -> {
                final String input = exception.getInput();
                sender.sendMessage(Component.text("The value " + input + " is invalid!", NamedTextColor.RED));
            });

            addSyntax((sender, context) -> {
                final int number = context.get(numberArgument);

                if (number < 0) {
                    sender.sendMessage(Component.text("Factorial is undefined for negative numbers!", NamedTextColor.RED));
                    return;
                }

                long factorial = calculateFactorial(number);
                sender.sendMessage(Component.text("The factorial of " + number + " is: " + factorial, NamedTextColor.GREEN));
            }, numberArgument);
        }

        private long calculateFactorial(int n) {
            if (n == 0 || n == 1) {
                return 1;
            }
            return n * calculateFactorial(n - 1);
        }
    }

    static class CmdCmd extends Command {
        public CmdCmd() {
            super("cmd");

            setDefaultExecutor((sender, context) -> sender.sendMessage(Component.text("Usage: /utils cmd <unregister/register> <command>", NamedTextColor.GRAY)));

            ArgumentWord actionArgument = ArgumentType.Word("action").from("unregister", "register");
            ArgumentString commandArgument = ArgumentType.String("command");

            addSyntax((sender, context) -> {
                String action = context.get(actionArgument);
                String commandName = context.get(commandArgument);

                switch (action) {
                    case "unregister" -> {
                        if (commandName.equalsIgnoreCase("utils") || commandName.equalsIgnoreCase("utils cmd")) {
                            sender.sendMessage(Component.text("You cannot unregister the '" + commandName + "' command.", NamedTextColor.RED));
                        } else {
                            Command command = MinecraftServer.getCommandManager().getCommand(commandName);
                            if (command != null) {
                                MinecraftServer.getCommandManager().unregister(command);
                                for (Player player : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
                                    player.sendMessage(Component.text("Command '" + commandName + "' has been unregistered.", NamedTextColor.GREEN));
                                    player.refreshCommands();
                                }
                            } else {
                                sender.sendMessage(Component.text("Command '" + commandName + "' not found.", NamedTextColor.RED));
                            }
                        }
                    }
                    case "register" -> {
                        String commandClassName = "dev.debutter.weirdlycubed.commands." + context.get(commandArgument) + "Cmd";
                        try {
                            Class<?> commandClass = Class.forName(commandClassName);
                            Command commandInstance = (Command) commandClass.getDeclaredConstructor().newInstance();
                            MinecraftServer.getCommandManager().register(commandInstance);
                            for (Player player : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
                                player.sendMessage(Component.text("Command '" + commandClassName + "' has been registered.", NamedTextColor.GREEN));
                                player.refreshCommands();
                            }
                        } catch (ClassNotFoundException e) {
                            sender.sendMessage(Component.text("Command class '" + commandClassName + "' not found.", NamedTextColor.RED));
                        } catch (Exception e) {
                            sender.sendMessage(Component.text("Error registering command: " + e.getMessage(), NamedTextColor.RED));
                        }
                    }
                    default -> throw new IllegalStateException("Unexpected value: " + action);
                }
            }, actionArgument, commandArgument);
        }
    }
}