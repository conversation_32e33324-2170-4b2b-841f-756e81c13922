package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.PermissionsHandler;
import dev.debutter.weirdlycubed.server.BannedPlayer;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.Garden;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import dev.debutter.weirdlycubed.utils.objects.MojangResponse;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.Argument;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.entity.Player;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

public class BanCmd extends Command {

    public BanCmd() {
        super("ban");

        setDefaultExecutor((sender, context) ->
                sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.ban.usage"))
        );

        Argument<String> playerArgument = ArgumentType.String("player");
        Argument<String> reasonArgument = ArgumentType.String("reason")
                .setDefaultValue("Banned by an operator");

        addSyntax((sender, context) -> {
            if (!PermissionsHandler.isOp(sender.identity().uuid())) {
                sender.sendMessage(AwesomerText.beautifyMessage("<red>You do not have permission to use this command."));
                return;
            }

            String playerName = context.get(playerArgument);
            String reason = context.get(reasonArgument);

            Player target = null;
            for (Player onlinePlayer : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
                if (onlinePlayer.getUsername().equalsIgnoreCase(playerName)) {
                    target = onlinePlayer;
                    break;
                }
            }

            if (target != null) {
                @Nullable MojangResponse.User data = Garden.getMojangData(playerName);
                if (data == null) {
                    sender.sendMessage(AwesomerText.beautifyMessage(
                            "<red>Could not fetch UUID for player '" + playerName + "'."));
                } else {
                    UUID uuid = data.id();

                    target.kick(AwesomerText.beautifyMessage("<red>You have been banned: " + reason));
                    sender.sendMessage(AwesomerText.beautifyMessage("<green>Banned player '" + playerName + "' for: " + reason));

                    BannedPlayer.banPlayer(uuid, reason);
                }
            } else {
                sender.sendMessage(AwesomerText.beautifyMessage("<red>Player '" + playerName + "' not found."));
            }

        }, playerArgument, reasonArgument);
    }
}
