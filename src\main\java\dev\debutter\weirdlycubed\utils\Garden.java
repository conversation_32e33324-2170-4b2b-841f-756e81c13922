package dev.debutter.weirdlycubed.utils;

import com.google.gson.Gson;
import dev.debutter.weirdlycubed.utils.objects.MojangResponse;
import net.minestom.server.coordinate.Point;
import net.minestom.server.coordinate.Vec;
import net.minestom.server.entity.ItemEntity;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.time.Duration;

public class Garden {

    private static final Duration PICKUP_DELAY = Duration.ofSeconds(1);

    public static @Nullable MojangResponse.User getMojangData(String username) {
        try {
            URL url = new URL("https://api.ashcon.app/mojang/v2/user/" + username);
            InputStreamReader reader = new InputStreamReader(url.openStream());
            return new Gson().from<PERSON>son(reader, MojangResponse.User.class);
        } catch (IOException e) {
            ConsoleLogger.exception(e);
            return null;
        }
    }

    public static void spawnItemEntity(Instance instance, Point blockPos, ItemStack itemStack) {
        ItemEntity itemEntity = new ItemEntity(itemStack);
        Vec velocity = new Vec(Math.random() - 0.5, Math.random() * 0.5, Math.random() - 0.5).mul(3);
        itemEntity.setVelocity(velocity);
        itemEntity.setInstance(instance, blockPos);
        itemEntity.setPickupDelay(PICKUP_DELAY);
    }

    /** @return Whether a block is water or waterlogged */
    public static boolean isWaterLogged(Block block) { // TODO: check if blocks like kelp or seagrass are correct
        if (block.id() == Block.WATER.id()) return true;

        @Nullable String waterlogged = block.getProperty("waterlogged");
        return waterlogged != null && waterlogged.equals("true");
    }

}
