package dev.debutter.weirdlycubed.player.data.serializer;

import dev.debutter.weirdlycubed.player.data.GenericSerializer;
import dev.debutter.weirdlycubed.utils.objects.ContainerItem;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.nbt.ListBinaryTag;
import net.minestom.server.entity.Player;
import net.minestom.server.inventory.PlayerInventory;
import net.minestom.server.item.ItemStack;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class InventorySerializer extends GenericSerializer<List<ContainerItem>> {

    @Override
    public void applyTo(@NotNull CompoundBinaryTag data, Player player) {
        PlayerInventory inventory = player.getInventory();
        ListBinaryTag elements = data.getList(getTag().getKey());

        for (int i = 0; i < elements.size(); i++) {
            CompoundBinaryTag element = elements.getCompound(i);

            int slot = element.getInt("slot");
            CompoundBinaryTag itemNBT = element.getCompound("item");
            ItemStack item = ItemStack.fromItemNBT(itemNBT);

            inventory.replaceItemStack(slot, (oldItem) -> item);
        }
    }

    @NotNull
    @Override
    public Tag<List<ContainerItem>> getTag() {
        return Tag.Structure("inventory", ContainerItem.class).list();
    }

    @NotNull
    @Override
    public List<ContainerItem> serialize(Player player) {
        PlayerInventory inventory = player.getInventory();
        List<ContainerItem> inventoryContents = new ArrayList<>();

        for (int slot = 0; slot < inventory.getSize(); slot++) {
            ItemStack item = inventory.getItemStack(slot);

            // Skip air
            if (item.isAir()) continue;

            // Add container item
            inventoryContents.add(new ContainerItem(slot, item));
        }

        return inventoryContents;
    }
}
