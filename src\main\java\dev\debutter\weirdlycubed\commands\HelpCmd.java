package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.text.Component;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.CommandSender;
import net.minestom.server.command.builder.CommandContext;

public class HelpCmd extends Command {

    public HelpCmd() {
        super("help");

        setDefaultExecutor(this::listCommands);
    }

    private void listCommands(CommandSender sender, CommandContext context) {
        Component message = AwesomerText.beautifyMessage("<gold>List of commands:" +
                "<newline><white>/distance <dark_gray>- <gray>Calculates the Distance between 2 players" +
                "<newline><white>/msg <dark_gray>- <gray>Messages a Player" +
                "<newline><white>/r <dark_gray>- <gray>Replies to a Player" +
                "<newline><white>/stop <dark_gray>- <gray>Stops the Server" +
                "<newline><white>/test <dark_gray>- <gray>Is a Test Command" +
                "<newline><white>/tp <dark_gray>- <gray>Teleports one player to another" +
                "<newline><white>/utils <dark_gray>- <gray>The Utils Command Contains (Factorials, Random Numbers, and Command (un)Register" +
                "<newline><white>/chat <dark_gray>- <gray>The Chat Command Contains (setPrefix, and setColor)" +
                "<newline><white>/gamemode|/gm <dark_gray>- <gray>Sets the Players Game mode");
        sender.sendMessage(message);
    }
}
