package dev.debutter.weirdlycubed.player.data;

import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.minestom.server.entity.Player;
import net.minestom.server.tag.Tag;
import net.minestom.server.tag.TagHandler;
import org.jetbrains.annotations.NotNull;

public abstract class GenericSerializer<T> {

    /** Appends the serialized player data to a tag handler */
    public final void writeTo(@NotNull TagHandler tagHandler, Player player) {
        tagHandler.setTag(getTag(), serialize(player));
    }

    /** Applies new data to a player */
    public abstract void applyTo(@NotNull CompoundBinaryTag data, Player player);
    /** @return The tag key of the data */
    public abstract @NotNull Tag<T> getTag();
    /** @return An object that represents the serialize data from a player */
    public abstract @NotNull T serialize(Player player);
}
