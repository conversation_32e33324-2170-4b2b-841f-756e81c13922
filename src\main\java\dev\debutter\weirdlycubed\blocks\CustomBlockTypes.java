package dev.debutter.weirdlycubed.blocks;

import dev.debutter.weirdlycubed.blocks.types.Cattail;
import dev.debutter.weirdlycubed.blocks.types.CattailStalk;
import dev.debutter.weirdlycubed.blocks.types.Chest;
import dev.debutter.weirdlycubed.blocks.types.GenericBlock;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import net.minestom.server.instance.block.Block;
import org.jetbrains.annotations.Nullable;

public enum CustomBlockTypes {

    GRASS_BLOCK(new GenericBlock(Block.GRASS_BLOCK, "grass_block", CustomItemTypes.GRASS_BLOCK, 10, "block.grass.break", 0.8f)),
    DIRT(new GenericBlock(Block.DIRT, "dirt", CustomItemTypes.DIRT, 10, "block.gravel.break", 0.8f)),
    STONE(new GenericBlock(Block.STONE, "stone", CustomItemTypes.STONE, 200, "block.stone.break", 0.8f)),
    SAND(new GenericBlock(Block.SAND, "sand", CustomItemTypes.SAND, 10, "block.sand.break", 0.8f)),
    CHEST(new Chest()),
    SHORT_GRASS(new GenericBlock(Block.SHORT_GRASS, "short_grass", CustomItemTypes.GRASS_BLADES, 1, "block.grass.break", 0.8f)), // FIXME: mining duration not doing anything
    ROCK(new GenericBlock(Block.STONE_BUTTON.withProperty("face","floor"), "rock", CustomItemTypes.ROCK, 20, "block.stone.break", 0.8f)),
    CATTAIL(new Cattail()),
    CATTAIL_STALK(new CattailStalk());

    private final CustomBlock handler;

    CustomBlockTypes(CustomBlock handler) {
        this.handler = handler;
    }

    public CustomBlock getHandler() {
        return handler;
    }
    public Block getBlock() {
        return handler.getBlock();
    }

    public static @Nullable CustomBlockTypes getFromId(String id) {
        for (CustomBlockTypes customBlockType : CustomBlockTypes.values()) {
            if (customBlockType.getHandler().getId().equals(id)) {
                return customBlockType;
            }
        }
        return null;
    }

}
