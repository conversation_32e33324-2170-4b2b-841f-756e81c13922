package dev.debutter.weirdlycubed.instances.generators.features;

import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.generator.GenerationUnit;

import java.util.Objects;
import java.util.Random;

public class Hills extends Biome {

    private final long foliageSeed;

    public Hills(long seed) {
        super(seed);

        Random rng = new Random(seed);

        this.foliageSeed = rng.nextLong();
    }

    @Override
    public void generateTerrain(GenerationUnit unit, Point bottom, int stoneHeight, int waterLevel, boolean favorLower) {
        for (int y = 0; y <= stoneHeight; y++) {
            unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.STONE.getBlock());
        }

        int dirtLayers = 3;

        for (int layer = 0; layer < dirtLayers; layer++) {
            int dirtBase = stoneHeight + 1 + layer * 2;

            for (int y = dirtBase; y < dirtBase + 2; y++) {
                unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.DIRT.getBlock());
            }
        }

        int grassBase = stoneHeight + 1 + dirtLayers * 2;
        if (grassBase > waterLevel) {
            if (grassBase == waterLevel + 1 && favorLower) {
                unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.SAND.getBlock());
            } else {
                unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.GRASS_BLOCK.getBlock());

                int foliage = new Random(Objects.hash(foliageSeed, bottom.x(), grassBase + 1, bottom.z())).nextInt(100);

                if (foliage < 15) {
                    unit.modifier().setBlock(bottom.withY(grassBase + 1), CustomBlockTypes.SHORT_GRASS.getBlock());
                } else if (foliage < 16) {
                    unit.modifier().setBlock(bottom.withY(grassBase + 1), CustomBlockTypes.ROCK.getBlock());
                }
            }
        } else {
            unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.SAND.getBlock());

            for (int y = grassBase + 1; y <= waterLevel; y++) {
                unit.modifier().setBlock(bottom.withY(y), Block.WATER);
            }
        }
    }
}
