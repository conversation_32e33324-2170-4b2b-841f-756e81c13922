package dev.debutter.weirdlycubed.crafting.matrix;

import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CraftingMatrix implements Cloneable {

    private final ItemStack[] items = new ItemStack[9];

    /**
     * Sets a given slot to an item stack
     * @return A reference to this object
     */
    public CraftingMatrix setSlot(Slot slot, ItemStack item) {
        items[slot.getIndex()] = item;
        return this;
    }

    public @NotNull ItemStack getSlot(Slot slot) {
        @Nullable ItemStack item = items[slot.getIndex()];

        if (item == null) {
            return ItemStack.AIR;
        }

        return item;
    }

    @Override
    public CraftingMatrix clone() {
        try {
            return (CraftingMatrix) super.clone();
        } catch (Exception e) {
            CraftingMatrix copy = new CraftingMatrix();

            System.arraycopy(items, 0, copy.items, 0, 9);

            return copy;
        }
    }

    @Override
    public String toString() {
        return "CraftingMatrix" + Arrays.toString(items);
    }

    // Predefined shifting keys
    private static final List<List<Slot>> shiftSides = List.of(
        Slot.topSlots,
        Slot.bottomSlots,
        Slot.leftSlots,
        Slot.rightSlots
    );
    private static final List<List<Slot>> shiftNotSides = List.of(
        Slot.notTopSlots,
        Slot.notBottomSlots,
        Slot.notLeftSlots,
        Slot.notRightSlots
    );
    private static final List<Integer> shiftOffsets = List.of(
        -3, // Index offset required to translate up
        3, // Index offset required to translate down
        -1, // Index offset required to translate left
        1 // Index offset required to translate right
    );

    /**
     * Generates all possible combinations of this matrix while keeping the items in the same shape
     */
    public ArrayList<CraftingMatrix> jostle() { // TODO: test in a 3x3 crafting menu
        // Create an array of all combinations only containing the current matrix at the moment
        ArrayList<CraftingMatrix> combinations = new ArrayList<>();

        combinations.add(clone());

        // Loop through each shifting direction
        for (int i = 0; i < 4; i++) {
            List<Slot> sides = shiftSides.get(i);
            List<Slot> notSides = shiftNotSides.get(i);
            int offset = shiftOffsets.get(i);

            // Try shifting all the current combinations
            for (CraftingMatrix matrix : new ArrayList<>(combinations)) {
                for (int j = 0; j < 2; j++) {
                    // Check if the boundary has been hit
                    boolean boundary = false;

                    for (Slot slot : sides) {
                        if (matrix.items[slot.getIndex()] == null) {
                            boundary = true;
                            break;
                        }
                    }

                    if (boundary) break; // Break out of the loop if the boundary was hit

                    // Shift the matrix up
                    CraftingMatrix shiftedMatrix = new CraftingMatrix();

                    for (Slot slot : notSides) {
                        int newIndex = slot.getIndex() + offset;

                        shiftedMatrix.items[newIndex] = matrix.items[slot.getIndex()];
                    }

                    combinations.add(shiftedMatrix); // Add the new matrix to the combinations
                }
            }
        }

        return combinations;
    }

}
