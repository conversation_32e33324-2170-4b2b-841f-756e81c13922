package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.player.data.PlayerMode;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.ArgumentLiteral;
import net.minestom.server.entity.GameMode;
import net.minestom.server.entity.Player;

public class GodCmd extends Command {

    public GodCmd() {
        super("god");

        ArgumentLiteral creativeArg = new ArgumentLiteral("c");

        setDefaultExecutor((sender, context) -> {
            if (!(sender instanceof Player player)) {
                sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.players_only", sender));
                return;
            }

            PlayerRuntimeData.setPlayerMode(player, PlayerMode.GOD);
            player.sendMessage(LocaleManager.getBeautifiedMessage("commands.god.activate", player));
        });

        addSyntax((sender, context) -> { // TODO: remove later, only here for testing purposes
            if (!(sender instanceof Player player)) {
                sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.players_only", sender));
                return;
            }

            PlayerRuntimeData.setPlayerMode(player, PlayerMode.GOD);
            player.setGameMode(GameMode.CREATIVE);
            player.sendMessage(LocaleManager.getBeautifiedMessage("commands.god.activate", player));
        }, creativeArg);
    }
}
