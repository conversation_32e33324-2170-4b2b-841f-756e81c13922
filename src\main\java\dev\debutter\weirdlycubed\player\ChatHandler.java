package dev.debutter.weirdlycubed.player;

import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import net.kyori.adventure.text.Component;
import net.minestom.server.MinecraftServer;
import net.minestom.server.entity.Player;
import net.minestom.server.event.player.PlayerChatEvent;
import org.jetbrains.annotations.NotNull;

public class ChatHandler {

    // TODO: not the correct way to do this, should set the message format instead
    public static void onPlayerChat(@NotNull PlayerChatEvent event) {
        Player player = event.getPlayer();
        String message = event.getRawMessage();
        String playerName = player.getUsername();
        ConsoleLogger.logPlayerChat(playerName, message);
        event.setCancelled(true);

        String prefix = PlayerRuntimeData.getPrefix(player);
        String chatColor = PlayerRuntimeData.getChatColor(player);

        // Check if the chatColor is already in the message
        if (!message.contains(chatColor)) {
            message = chatColor + message;
        }

        // Apply the MiniMessage styling to the message
        Component styledMessage = AwesomerText.stylize(message);

        Component modifiedMessage = Component.text(playerName + ": ").append(styledMessage);
        if (!prefix.isEmpty()) {
            modifiedMessage = Component.text("[" + prefix + "] ").append(modifiedMessage);
        }

        for (Player onlinePlayer : MinecraftServer.getConnectionManager().getOnlinePlayers()) {
            // Send the styled message
            onlinePlayer.sendMessage(modifiedMessage);
        }
    }

}
