package dev.debutter.weirdlycubed.items;

import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.text.Component;

public enum ItemType {

    // TODO: use prettier colors

    /** Items that can be placed in the world */
    BLOCK(AwesomerText.beautifyMessage("<white>Block</white>")),
    /** Items that are plant based */
    PLANT(AwesomerText.beautifyMessage("<green>Plant</green>")),
    /** Items that can be eaten */
    EDIBLE(AwesomerText.beautifyMessage("<yellow>Edible</yellow>")),
    /** Items that are used in make other things */
    MATERIAL(AwesomerText.beautifyMessage("<#FC8B62>Material</#FC8B62>"));

    private final Component message;

    ItemType(Component message) {
        this.message = message;
    }

    public Component getComponent() {
        return message;
    }
}
