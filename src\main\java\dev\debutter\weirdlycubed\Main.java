package dev.debutter.weirdlycubed;

import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import dev.debutter.weirdlycubed.blocks.listeners.BreakBlockHandler;
import dev.debutter.weirdlycubed.blocks.listeners.PlaceBlockHandler;
import dev.debutter.weirdlycubed.commands.*;
import dev.debutter.weirdlycubed.instances.WorldManager;
import dev.debutter.weirdlycubed.instances.WorldTicker;
import dev.debutter.weirdlycubed.inventories.InvMenu;
import dev.debutter.weirdlycubed.inventories.menus.ItemsMenu;
import dev.debutter.weirdlycubed.player.*;
import dev.debutter.weirdlycubed.server.GameSaver;
import dev.debutter.weirdlycubed.server.OppedPlayer;
import dev.debutter.weirdlycubed.server.ResourcePackHandler;
import dev.debutter.weirdlycubed.server.ServerPingHandler;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import dev.debutter.weirdlycubed.utils.PersistentStorage;
import dev.dejvokep.boostedyaml.YamlDocument;
import dev.dejvokep.boostedyaml.dvs.versioning.BasicVersioning;
import dev.dejvokep.boostedyaml.settings.dumper.DumperSettings;
import dev.dejvokep.boostedyaml.settings.general.GeneralSettings;
import dev.dejvokep.boostedyaml.settings.loader.LoaderSettings;
import dev.dejvokep.boostedyaml.settings.updater.UpdaterSettings;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.StaticHandler;
import me.lucko.spark.minestom.SparkMinestom;
import net.minestom.server.MinecraftServer;
import net.minestom.server.event.GlobalEventHandler;
import net.minestom.server.event.instance.InstanceChunkUnloadEvent;
import net.minestom.server.event.instance.InstanceTickEvent;
import net.minestom.server.event.inventory.*;
import net.minestom.server.event.item.ItemDropEvent;
import net.minestom.server.event.item.PickupItemEvent;
import net.minestom.server.event.player.*;
import net.minestom.server.event.server.ServerListPingEvent;
import net.minestom.server.extras.MojangAuth;
import net.minestom.server.extras.lan.OpenToLAN;
import net.minestom.server.extras.velocity.VelocityProxy;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.listener.manager.PacketListenerManager;
import net.minestom.server.network.ConnectionManager;
import net.minestom.server.network.packet.client.play.ClientCloseWindowPacket;
import net.minestom.server.timer.TaskSchedule;
import org.jetbrains.annotations.Nullable;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.*;
import java.nio.file.FileSystem;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class Main {

    private static YamlDocument config;
    private static final Set<YamlDocument> yamlDocuments = new HashSet<>();
    private static final Set<io.vertx.core.http.ServerWebSocket> sockets = ConcurrentHashMap.newKeySet();

    public static void main(String[] args) {
        // Get the config
        try {
            config = YamlDocument.create(new File("config/config.yml"),
                    Objects.requireNonNull(Main.class.getResourceAsStream("/config/config.yml")),
                    GeneralSettings.DEFAULT,
                    LoaderSettings.builder().setAutoUpdate(true).build(),
                    DumperSettings.DEFAULT,
                    UpdaterSettings.builder().setVersioning(new BasicVersioning("config-version"))
                            .setOptionSorting(UpdaterSettings.OptionSorting.SORT_BY_DEFAULTS).build()
            );

            config.update();
            config.save();

            addSafeSaveDocument(config);
        } catch (IOException e) {
            ConsoleLogger.exception("Could not create/load config.", e);
            System.exit(1);
        }

        fileCopy("/config/banned-players.json", "config/banned-players.json", "json");
        fileCopy("/config/ops.json", "config/ops.json", "json");
        fileCopy("/config/welcomemsg.json", "config/welcomemsg.json", "json");

        fileCopy("/web/home.html", "web/home.html", "web");
        fileCopy("/web/script.js", "web/script.js", "web");
        fileCopy("/web/styling.css", "web/styling.css", "web");

        // Create resource pack zip file
        try {
            // Make sure the parent directory exists
            ResourcePackHandler.resourcePackFile.getParentFile().mkdirs();

            // Get the resource pack path
            URI packURI = Main.class.getResource("/pack/").toURI();
            Path packPath;
            @Nullable FileSystem fs = null;

            if ("jar".equals(packURI.getScheme())) {
                fs = FileSystems.newFileSystem(packURI, Map.of());
                packPath = fs.getPath("/pack/");
            } else {
                packPath = Path.of(packURI);
            }

            // Create the resource pack
            try (
                OutputStream outputStream = new FileOutputStream(ResourcePackHandler.resourcePackFile);
                ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);
                Stream<Path> walk = Files.walk(packPath)
            ) {
                // Traverse through the pack's files
                walk.filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        // Get the relative path
                        String relativePath = packPath.relativize(path).toString().replace("\\", "/");

                        // Add each file into the zip file
                        ZipEntry zipEntry = new ZipEntry(relativePath);

                        try {
                            zipOutputStream.putNextEntry(zipEntry);
                            Files.copy(path, zipOutputStream);
                            zipOutputStream.closeEntry();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
            }

            if (fs != null) fs.close();

            // Print successful file creation
            ConsoleLogger.info("Updated resource pack file cache");
        } catch (NullPointerException | URISyntaxException | IOException e) {
            ConsoleLogger.exception("Failed to create resource pack", e);
        }

        // Initialize the Minecraft server
        init();

        // Initialize the HTTP server
        startHttpServer();
    }

    /**
     * @param resourcePath the resource path within the jar
     * @param targetPath   the destination path
     * @param type         a type identifier (e.g., "json" or "web")
     */
    private static void fileCopy(String resourcePath, String targetPath, String type) {
        File outFile = new File(targetPath);
        if (outFile.exists()) {
            return;
        }
        outFile.getParentFile().mkdirs();

        try (InputStream in = Main.class.getResourceAsStream(resourcePath)) {
            if (in == null) {
                ConsoleLogger.warning("Resource not found in jar: " + resourcePath);
                return;
            }
            Files.copy(in, outFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            ConsoleLogger.info("Copied " + type + " resource " + resourcePath + " to " + targetPath);
        } catch (IOException e) {
            ConsoleLogger.exception("Could not copy " + type + " resource " + resourcePath + " to " + targetPath, e);
        }
    }

    private static void init() {
        MinecraftServer minecraftServer = MinecraftServer.init();

        MinecraftServer.setBrandName(Constants.PROJECT_NAME);

        if (config.getBoolean("velocity.enabled")) {
            VelocityProxy.enable(config.getString("velocity.secret"));
        } else {
            MojangAuth.init();
        }

        // Register custom blocks
        for (CustomBlockTypes customBlockType : CustomBlockTypes.values()) {
            BlockHandler customBlockHandler = customBlockType.getHandler();
            MinecraftServer.getBlockManager().registerHandler(customBlockHandler.getKey(), () -> customBlockHandler);
        }

        // Initialize the world
        WorldManager.init();

        // Register ticker, events and commands
        startTicker();
        registerEvents();
        registerCommands();

        // Start the server
        int port = config.getInt("port");
        minecraftServer.start("0.0.0.0", port);
        ConsoleLogger.info("Server started on port " + port);

        // Initialize spark instance
        Path directory = Path.of("spark");
        SparkMinestom.builder(directory)
                .commands(true)
                .permissionHandler((sender, permission) -> OppedPlayer.isPlayerOp(sender.identity().uuid()))
                .enable();

        // Add shutdown handler
        MinecraftServer.getSchedulerManager().buildShutdownTask(() -> {
            ConsoleLogger.info("Server is shutting down.");

            // Save data
            GameSaver.save();

            // Kick all players connected to the server
            ConnectionManager connectionManager = MinecraftServer.getConnectionManager();
            connectionManager.getOnlinePlayers().forEach(player -> {
                player.kick("Server is closing.");
                connectionManager.removePlayer(player.getPlayerConnection());
            });

            OpenToLAN.close();

            // Save all YAML documents
            yamlDocuments.forEach(document -> {
                try {
                    document.save();
                } catch (IOException e) {
                    ConsoleLogger.exception("Failed to save file " + document.getFile(), e);
                }
            });

            MinecraftServer.getServer().stop();
            System.exit(0);
        });
    }

    public static void addSafeSaveDocument(YamlDocument document) {
        yamlDocuments.add(document);
    }

    private static void startTicker() {
        MinecraftServer.getSchedulerManager().scheduleTask(() -> {
            // Every tick
            BreakBlockHandler.tickRepair();
            GameSaver.tick();
        }, TaskSchedule.immediate(), TaskSchedule.tick(1));
    }

    private static void registerEvents() {
        GlobalEventHandler globalEventHandler = MinecraftServer.getGlobalEventHandler();

        globalEventHandler
            .addListener(PlayerSpawnEvent.class, PlayerConnection::onPlayerJoin)
            .addListener(PlayerDisconnectEvent.class, PlayerConnection::onPlayerDisconnect)
            .addListener(ItemDropEvent.class, ItemHandler::onPlayerDropItem)
            .addListener(PickupItemEvent.class, ItemHandler::onPlayerPickupItem)
            .addListener(ServerListPingEvent.class, ServerPingHandler::onServerPing)
            .addListener(PlayerChatEvent.class, ChatHandler::onPlayerChat)
            .addListener(PlayerStartDiggingEvent.class, BreakBlockHandler::onPlayerStartDigging)
            .addListener(InventoryCloseEvent.class, InvMenu::onInventoryClose)
            .addListener(InventoryClickEvent.class, InvMenu::onInventoryClick)
            .addListener(InventoryOpenEvent.class, InvMenu::onInventoryOpen)
            .addListener(InventoryPreClickEvent.class, InvMenu::onInventoryPreClick)
            .addListener(InventoryItemChangeEvent.class, InvMenu::onInventoryItemChange)
            .addListener(PlayerCancelDiggingEvent.class, BreakBlockHandler::onPlayerStopDigging)
            .addListener(AsyncPlayerConfigurationEvent.class, PlayerConnection::onPlayerConfiguration)
            .addListener(PlayerBlockPlaceEvent.class, PlaceBlockHandler::onPlaceBlock)
            .addListener(PlayerBlockBreakEvent.class, BreakBlockHandler::onPlayerBlockBreak)
            .addListener(PlayerSwapItemEvent.class, event -> {
                event.getPlayer().openInventory(new ItemsMenu());
                event.setCancelled(true);
            })
            .addListener(PlayerCommandEvent.class, event -> {
                String playerName = event.getPlayer().getUsername();
                String commandName = event.getCommand();

                ConsoleLogger.logCommandExecution(playerName, commandName);
            })
            .addListener(InventoryItemChangeEvent.class, InventoryHandler::onInventoryItemChange)
            .addListener(InventoryPreClickEvent.class, InventoryHandler::onInventoryClick)
            .addListener(PlayerResourcePackStatusEvent.class, ResourcePackHandler::onResourcePackStatus)
            .addListener(InstanceTickEvent.class, WorldTicker::onTick)
            .addListener(PlayerTickEvent.class, PlayerTicker::onTick)
            .addListener(InstanceChunkUnloadEvent.class, WorldManager::onChunkUnload);

        PacketListenerManager packetListenerManager = MinecraftServer.getPacketListenerManager();

        packetListenerManager
            .setPlayListener(ClientCloseWindowPacket.class, (event, player) -> {
                if (player.getOpenInventory() == null) InventoryHandler.onInventoryClose(player);

                player.closeInventory();
            });
    }

    private static void registerCommands() {
        MinecraftServer.getCommandManager().register(new KickCmd());
        MinecraftServer.getCommandManager().register(new BanCmd());
        MinecraftServer.getCommandManager().register(new OpCmd());
        MinecraftServer.getCommandManager().register(new UtilsCmd());
        MinecraftServer.getCommandManager().register(new TpCmd());
        MinecraftServer.getCommandManager().register(new StopCmd());
        MinecraftServer.getCommandManager().register(new HelpCmd());
        MinecraftServer.getCommandManager().register(new MsgCmd());
        MinecraftServer.getCommandManager().register(new ReplyCmd());
        MinecraftServer.getCommandManager().register(new SurvivalCmd());
        MinecraftServer.getCommandManager().register(new GodCmd());
        MinecraftServer.getCommandManager().register(new GhostCmd());
    }

    public static YamlDocument getConfig() {
        return config;
    }

    public static void startHttpServer() {
        // Create Vert.x instance and router
        Vertx vertx = Vertx.vertx();
        Router router = Router.router(vertx);

        router.get("/resource-pack.zip").handler(ResourcePackHandler::serveResourcePack);

        router.get("/ws").handler(ctx -> {
            ctx.request().toWebSocket().onSuccess(ws -> {
                sockets.add(ws);
                ws.closeHandler(v -> sockets.remove(ws));
            }).onFailure(err -> {
                ConsoleLogger.exception("Failed to establish websocket", err);
            });
        });

        router.route().handler(StaticHandler.create("web")
                .setIndexPage("home.html")
                .setCachingEnabled(false));

        // Start HTTP server
        int port = config.getInt("web.port");

        vertx.createHttpServer()
                .requestHandler(router)
                .listen(port)
                .onComplete(http -> {
                    if (http.succeeded()) {
                        ConsoleLogger.info("HTTP server started on port " + port);
                    } else {
                        ConsoleLogger.exception("Failed to start HTTP server", http.cause());
                    }
                });

        vertx.setPeriodic(1000, id -> {
            JsonObject consoleMsg = new JsonObject();
            consoleMsg.put("type", "console");
            String[] logs = ConsoleLogger.getAndClearLogBuffer();
            JsonArray dataArray = new JsonArray();
            for (String log : logs) {
                dataArray.add(log);
            }
            consoleMsg.put("data", dataArray);
            sockets.forEach(ws -> ws.writeTextMessage(consoleMsg.encode()));
        });

        vertx.setPeriodic(2000, id -> {
            JsonObject playersMsg = new JsonObject();
            playersMsg.put("type", "players");
            JsonArray playersArray = new JsonArray();
            MinecraftServer.getConnectionManager().getOnlinePlayers().forEach(player -> {
                JsonObject playerJson = new JsonObject();
                playerJson.put("uuid", player.getUuid().toString());
                playerJson.put("name", player.getUsername());
                playersArray.add(playerJson);
            });
            playersMsg.put("data", playersArray);
            sockets.forEach(ws -> ws.writeTextMessage(playersMsg.encode()));
        });
    }
}
