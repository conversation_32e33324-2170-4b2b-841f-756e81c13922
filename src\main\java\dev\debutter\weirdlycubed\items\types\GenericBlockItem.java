package dev.debutter.weirdlycubed.items.types;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.items.ItemBuilder;
import dev.debutter.weirdlycubed.items.ItemRarity;
import dev.debutter.weirdlycubed.items.ItemType;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.text.Component;
import net.minestom.server.item.Material;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class GenericBlockItem extends ItemBuilder {

    private final Material itemMaterial;
    private final String customBlockId;
    private final ItemRarity rarity;
    private final @Nullable Component itemName;

    public GenericBlockItem(@NotNull Material itemMaterial, @NotNull String customBlockId, ItemRarity rarity) {
        this.itemMaterial = itemMaterial;
        this.customBlockId = customBlockId;
        this.rarity = rarity;
        this.itemName = null;
    }

    public GenericBlockItem(@NotNull Material itemMaterial, @NotNull String customBlockId, ItemRarity rarity, @NotNull String itemName) {
        this.itemMaterial = itemMaterial;
        this.customBlockId = customBlockId;
        this.rarity = rarity;
        this.itemName = AwesomerText.beautifyMessage(itemName);
    }

    @NotNull
    @Override
    public String id() {
        return "place_" + customBlockId;
    }

    @NotNull
    @Override
    protected Material material() {
        return itemMaterial;
    }

    @Nullable
    @Override
    protected Component name() {
        return itemName;
    }

    @Nullable
    @Override
    public ItemRarity rarity() {
        return rarity;
    }

    @Nullable
    @Override
    public List<ItemType> types() {
        return List.of(ItemType.BLOCK);
    }

    @Nullable
    @Override
    public Component description() {
        return null;
    }

    @Nullable
    @Override
    protected CompoundBinaryTag.Builder data() {
        return CompoundBinaryTag.builder()
            .putString(CustomBlock.BLOCK_ITEM_TAG.getKey(), customBlockId);
    }
}
