package dev.debutter.weirdlycubed.player.data.serializer;

import dev.debutter.weirdlycubed.player.data.GenericSerializer;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.kyori.adventure.nbt.ListBinaryTag;
import net.minestom.server.coordinate.Pos;
import net.minestom.server.entity.Player;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class RotationSerializer extends GenericSerializer<List<Float>> {

    @Override
    public void applyTo(@NotNull CompoundBinaryTag data, Player player) {
        ListBinaryTag elements = data.getList(getTag().getKey());
        if (elements.isEmpty()) return; // Cancel if data is not defined

        Pos position = player.getPosition();

        float yaw = elements.getFloat(0);
        float pitch = elements.getFloat(1);

        player.teleport(new Pos(
            position.x(),
            position.y(),
            position.z(),
            yaw,
            pitch
        ));
    }

    @NotNull
    @Override
    public Tag<List<Float>> getTag() {
        return Tag.Float("rotation").list();
    }

    @NotNull
    @Override
    public List<Float> serialize(Player player) {
        Pos position = player.getPosition();

        return List.of(
            position.yaw(),
            position.pitch()
        );
    }
}
