package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.ArgumentStringArray;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.command.builder.arguments.ArgumentWord;
import net.minestom.server.entity.Player;

public class MsgCmd extends Command {
    public MsgCmd() {
        super("msg");

        final ArgumentWord targetArg = ArgumentType.Word("target");
        final ArgumentStringArray messageArg = ArgumentType.StringArray("message");

        setDefaultExecutor((sender, context) ->
            sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.msg.usage"))
        );

        addSyntax((sender, context) -> {
            String targetName = context.get(targetArg);
            Player target = MinecraftServer.getConnectionManager().getOnlinePlayerByUsername(targetName);

            if (target == null) {
                sender.sendMessage(Component.text("Player not found.", NamedTextColor.RED));
                return;
            }

            String[] messageArray = context.get(messageArg);
            String message = String.join(" ", messageArray);

            Player senderPlayer = (Player) sender;

            Component senderMsg = Component.text(senderPlayer.getUsername(), NamedTextColor.AQUA)
                    .append(Component.text(" -> ", NamedTextColor.GRAY))
                    .append(Component.text(target.getUsername(), NamedTextColor.AQUA))
                    .append(Component.text(" : ", NamedTextColor.DARK_GRAY))
                    .append(Component.text(message, NamedTextColor.GRAY));

            Component receiverMsg = Component.text(senderPlayer.getUsername(), NamedTextColor.AQUA)
                    .append(Component.text(" -> ", NamedTextColor.GRAY))
                    .append(Component.text(target.getUsername(), NamedTextColor.AQUA))
                    .append(Component.text(" : ", NamedTextColor.DARK_GRAY))
                    .append(Component.text(message, NamedTextColor.GRAY));

            senderPlayer.sendMessage(senderMsg);
            target.sendMessage(receiverMsg);

            Sound bitSound = Sound.sound(Key.key("block.note_block.bit"), Sound.Source.PLAYER, 0.5f, 1f);
            target.getInstance().playSound(bitSound, target.getPosition());

            PlayerRuntimeData.setReply(senderPlayer, target);
        }, targetArg, messageArg);
    }
}