package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.entity.Player;
import net.minestom.server.network.ConnectionManager;

import java.util.Optional;
import java.util.UUID;

public class ReplyCmd extends Command {
    public ReplyCmd() {
        super("reply", "r");

        setDefaultExecutor((sender, context) ->
            sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.reply.usage"))
        );

        addSyntax((sender, context) -> {
            Player senderPlayer = (Player) sender;
            UUID lastSender = PlayerRuntimeData.getReply(senderPlayer);
            ConnectionManager connectionManager = MinecraftServer.getConnectionManager();
            Optional<Player> targetOptional = connectionManager.getOnlinePlayers().stream()
                    .filter(player -> player.getUuid().equals(lastSender))
                    .findFirst();

            if (targetOptional.isPresent()) {
                Player target = targetOptional.get();
                String[] messageArray = context.get("message");

                String message = String.join(" ", messageArray);

                Component senderMsg = Component.text(senderPlayer.getUsername(), NamedTextColor.AQUA)
                        .append(Component.text(" -> ", NamedTextColor.GRAY))
                        .append(Component.text(target.getUsername(), NamedTextColor.AQUA))
                        .append(Component.text(" : ", NamedTextColor.DARK_GRAY))
                        .append(Component.text(message, NamedTextColor.GRAY));

                Component receiverMsg = Component.text(senderPlayer.getUsername(), NamedTextColor.AQUA)
                        .append(Component.text(" -> ", NamedTextColor.GRAY))
                        .append(Component.text(target.getUsername(), NamedTextColor.AQUA))
                        .append(Component.text(" : ", NamedTextColor.DARK_GRAY))
                        .append(Component.text(message, NamedTextColor.GRAY));

                senderPlayer.sendMessage(senderMsg);
                target.sendMessage(receiverMsg);

                Sound bitSound = Sound.sound(Key.key("block.note_block.bit"), Sound.Source.PLAYER, 0.5f, 1.5f);
                target.getInstance().playSound(bitSound, target.getPosition());

                PlayerRuntimeData.setReply(senderPlayer, target);
            } else {
                sender.sendMessage(Component.text("No one to reply to!", NamedTextColor.RED));
                PlayerRuntimeData.removeReply(senderPlayer);
            }
        }, ArgumentType.StringArray("message"));
    }
}
