package dev.debutter.weirdlycubed.items;

import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.text.Component;

public enum ItemRarity {

    // TODO: use prettier colors
    COMMON(1, "white"),
    <PERSON>COMM<PERSON>(2, "yellow"),
    RARE(3, "green"),
    EPIC(4, "aqua"),
    LEGENDARY(5, "light_purple");

    private static final int TOTAL_RARITIES = ItemRarity.values().length;

    private final int level;
    private final String color;

    ItemRarity(int level, String color) {
        this.level = level;
        this.color = color;
    }

    public Component getComponent() {
        return AwesomerText.beautifyMessage(
            "<color:" + this.color + ">" +
            "★".repeat(level) +
            "</color:" + this.color + "><darK_gray>" +
            "☆".repeat(TOTAL_RARITIES - level) +
            "</darK_gray>"
        );
    }

}
