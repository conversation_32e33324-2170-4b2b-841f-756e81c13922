package dev.debutter.weirdlycubed.utils.objects;

import org.jetbrains.annotations.Nullable;

import java.util.UUID;

public class MojangResponse {

    public record User(
        String uuid,
        String username,
        Textures textures,
        boolean legacy,
        boolean demo,
        @Nullable String created_at
    ) {
        public UUID id() {
            return UUID.fromString(uuid);
        }
    }

    public record Textures(
        boolean slim,
        boolean custom,
        SkinTexture skin,
        @Nullable CapeTexture cape,
        RawTexture raw
    ) {}

    public record SkinTexture(
        String url,
        String data
    ) {}

    public record CapeTexture(
        @Nullable String url,
        @Nullable String data
    ) {}

    public record RawTexture(
        String url,
        String data
    ) {}

}
