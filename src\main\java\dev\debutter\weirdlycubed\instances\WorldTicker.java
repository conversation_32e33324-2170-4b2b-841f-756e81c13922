package dev.debutter.weirdlycubed.instances;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.player.PlayerTicker;
import net.minestom.server.coordinate.BlockVec;
import net.minestom.server.entity.Player;
import net.minestom.server.event.instance.InstanceTickEvent;
import net.minestom.server.instance.Chunk;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.world.DimensionType;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Random;

public class WorldTicker {

    private static final Random tickRng = new Random();

    public static void onTick(@NotNull InstanceTickEvent event) {
        Instance instance = event.getInstance();

        // Tick players
        List<Player> onlinePlayers = instance.getPlayers().stream().toList();

        for (int i = 0; i < onlinePlayers.size(); i++) {
            Player player = onlinePlayers.get(i);

            for (int j = i + 1; j < onlinePlayers.size(); j++) {
                Player otherPlayer = onlinePlayers.get(j);

                // Every tick for each pair of two players
                PlayerTicker.tickPair(player, otherPlayer);
            }
        }

        // Tick all loaded chunks
        for (Chunk chunk : instance.getChunks()) {
            onTickChunk(chunk);
        }
    }

    public static void onTickChunk(Chunk chunk) {
        // Get a random block
        DimensionType dimensionType = chunk.getInstance().getCachedDimensionType();

        int randomX = tickRng.nextInt(16) + chunk.getChunkX() * Chunk.CHUNK_SIZE_X;
        int randomY = tickRng.nextInt(dimensionType.minY(), dimensionType.maxY());
        int randomZ = tickRng.nextInt(16) + chunk.getChunkZ() * Chunk.CHUNK_SIZE_Z;

        BlockVec point = new BlockVec(randomX, randomY, randomZ);
        Block block = chunk.getBlock(point);

        // Check its handler to see if it should be randomly ticked
        @Nullable BlockHandler handler = block.handler();

        if (handler instanceof CustomBlock customBlock && customBlock.isRandomlyTickable()) {
            customBlock.randomTick(new BlockHandler.Tick(block, chunk.getInstance(), point));
        }
    }

}
