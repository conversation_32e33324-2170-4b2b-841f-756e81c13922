package dev.debutter.weirdlycubed.instances.generators.features;

import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.generator.GenerationUnit;

public class Desert extends Biome {

    public Desert(long seed) {
        super(seed);
    }

    @Override
    public void generateTerrain(GenerationUnit unit, Point bottom, int stoneHeight, int waterLevel, boolean favorLower) {
        for (int y = 0; y <= stoneHeight; y++) {
            unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.STONE.getBlock());
        }

        int dirtLayers = 3;

        for (int layer = 0; layer < dirtLayers; layer++) {
            int dirtBase = stoneHeight + 1 + layer * 2;

            for (int y = dirtBase; y < dirtBase + 2; y++) {
                unit.modifier().setBlock(bottom.withY(y), CustomBlockTypes.DIRT.getBlock());
            }
        }

        int grassBase = stoneHeight + 1 + dirtLayers * 2;

        unit.modifier().setBlock(bottom.withY(grassBase), CustomBlockTypes.SAND.getBlock());

        if (grassBase <= waterLevel) {
            for (int y = grassBase + 1; y <= waterLevel; y++) {
                unit.modifier().setBlock(bottom.withY(y), Block.WATER);
            }
        }
    }
}
