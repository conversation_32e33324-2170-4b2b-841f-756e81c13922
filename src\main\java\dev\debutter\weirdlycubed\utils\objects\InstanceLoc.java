package dev.debutter.weirdlycubed.utils.objects;

import net.minestom.server.coordinate.Point;
import net.minestom.server.coordinate.Vec;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;

public record InstanceLoc(Instance instance, Point point) {

    /** @return The block that is currently at this location */
    public Block block() {
        return instance.getBlock(point);
    }

    public Vec blockCenter() {
        return new Vec(
            point.blockX() + 0.5d,
            point.blockY() + 0.5d,
            point.blockZ() + 0.5d
        );
    }

}
