document.addEventListener("DOMContentLoaded", function() {
    const consoleElement = document.getElementById("console");
    const playerListElement = document.getElementById("player-list");
    const socket = new WebSocket("ws://" + window.location.host + "/ws");

    function stripAnsi(text) {
        return text.replace(/\x1B\[[0-9;]*[a-zA-Z]/g, "");
    }

    socket.onmessage = function(event) {
        const message = JSON.parse(event.data);
        if (message.type === "console") {
            message.data.forEach(function(line) {
                const p = document.createElement("p");
                p.textContent = stripAnsi(line);
                consoleElement.appendChild(p);
            });
            while (consoleElement.childNodes.length > 100) {
                consoleElement.removeChild(consoleElement.firstChild);
            }
            consoleElement.scrollTop = consoleElement.scrollHeight;
        } else if (message.type === "players") {
            playerListElement.innerHTML = "";
            message.data.forEach(function(player) {
                const li = document.createElement("li");
                const img = document.createElement("img");
                img.src = "https://crafatar.com/avatars/" + player.uuid + "?size=64&overlay";
                const span = document.createElement("span");
                span.textContent = player.name;
                li.appendChild(img);
                li.appendChild(span);
                playerListElement.appendChild(li);
            });
        }
    };

    socket.onopen = function() {
        console.log("WebSocket connection established");
    };

    socket.onerror = function(error) {
        console.error("WebSocket error:", error);
    };
});