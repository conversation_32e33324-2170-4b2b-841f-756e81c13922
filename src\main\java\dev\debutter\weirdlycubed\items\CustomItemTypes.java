package dev.debutter.weirdlycubed.items;

import dev.debutter.weirdlycubed.items.types.*;
import net.minestom.server.item.ItemStack;
import net.minestom.server.item.Material;
import org.jetbrains.annotations.Nullable;

public enum CustomItemTypes {

    GRASS_BLOCK(new GenericBlockItem(Material.GRASS_BLOCK, "grass_block", ItemRarity.COMMON)),
    DIRT(new GenericBlockItem(Material.DIRT, "dirt", ItemRarity.COMMON)),
    STONE(new GenericBlockItem(Material.STONE, "stone", ItemRarity.COMMON)),
    SAND(new GenericBlockItem(Material.SAND, "sand", ItemRarity.COMMON)),
    CHEST(new GenericBlockItem(Material.CHEST, "chest", ItemRarity.COMMON)),
    GRASS_BLADES(new GenericPlantItem("grass_blades", Material.TALL_GRASS, "<#50CA57>Grass Blades</#50CA57>", ItemRarity.COMMON)),
    TWISTED_GRASS(new TwistedGrass()),
    ROCK(new Rock()),
    SHARP_ROCK(new SharpRock()),
    CATTAIL(new GenericBlockItem(Material.BROWN_CANDLE, "cattail", ItemRarity.COMMON, "<#f69d5d>Cattail</#f69d5d>"));

    private final ItemBuilder builder;

    CustomItemTypes(ItemBuilder builder) {
        this.builder = builder;
    }

    public ItemBuilder getBuilder() {
        return builder;
    }
    public ItemStack.Builder getItemBuilder() {
        return builder.generate();
    }
    public ItemStack getItemStack() {
        return builder.generate().build();
    }

    public static @Nullable CustomItemTypes getFromId(String id) {
        for (CustomItemTypes customItemType : CustomItemTypes.values()) {
            if (customItemType.getBuilder().id().equals(id)) {
                return customItemType;
            }
        }
        return null;
    }

}
