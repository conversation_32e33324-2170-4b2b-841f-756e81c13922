package dev.debutter.weirdlycubed.blocks;

import dev.debutter.weirdlycubed.utils.Garden;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

public interface CustomBlockHandler {

    @NotNull String getId();

    @NotNull Sound getBreakSound();
    int getMiningDuration();

    default ItemStack[] getItemDrops(@NotNull BlockHandler.Destroy destroy) {
        return new ItemStack[0];
    }

    default boolean canPlace(Instance instance, Point position) {
        return true;
    }

    /** @return A list of ids that this custom block can be replaced with */
    default boolean isReplaceableWith(CustomBlock customBlock) {
        return getId().equals(customBlock.getId());
    }

    default void onReplace(@NotNull BlockHandler.Destroy destroy) {}
    default void onBreak(@NotNull BlockHandler.Destroy destroy) {
        Instance instance = destroy.getInstance();
        Point point = destroy.getBlockPosition();
        InstanceLoc loc = new InstanceLoc(instance, point);
        Point blockCenterPos = loc.blockCenter();

        // Spawn item drops
        for (ItemStack itemDrop : getItemDrops(destroy)) {
            Garden.spawnItemEntity(instance, blockCenterPos, itemDrop);
        }

        // Play break sound
        instance.playSound(getBreakSound(), blockCenterPos);
    }

    default void randomTick(@NotNull BlockHandler.Tick tick) {}
    default boolean isRandomlyTickable() {
        return false;
    }

}
