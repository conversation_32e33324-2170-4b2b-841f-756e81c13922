package dev.debutter.weirdlycubed.inventories.menus;

import dev.debutter.weirdlycubed.inventories.InvMenu;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.event.inventory.*;
import net.minestom.server.inventory.InventoryType;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

public class ItemsMenu extends InvMenu {

    public ItemsMenu() {
        super(InventoryType.CHEST_3_ROW, AwesomerText.beautifyMessage("<rainbow>All the items you'll ever need</rainbow>"));
    }

    @Override
    protected void onOpen(@NotNull InventoryOpenEvent event) {
        CustomItemTypes[] customItemTypes = CustomItemTypes.values();

        for (int i = 0; i < Math.min(customItemTypes.length, 27); i++) {
            CustomItemTypes customItemType = customItemTypes[i];
            ItemStack itemStack = customItemType.getItemStack();

            this.addItemStack(itemStack);
        }
    }

    @Override
    protected void onClose(@NotNull InventoryCloseEvent event) {}

    @Override
    protected void onClick(@NotNull InventoryClickEvent event) {}

    @Override
    protected void onPreClick(@NotNull InventoryPreClickEvent event) {
        ItemStack clickedItem = event.getClickedItem();

        event.setCancelled(true);

        if (clickedItem.isAir()) return;

        event.getPlayer().playSound(Sound.sound(Key.key("entity.item.pickup"), Sound.Source.PLAYER, 0.5f, 1f));
        event.getPlayer().getInventory().addItemStack(clickedItem);
    }

    @Override
    protected void onItemChange(@NotNull InventoryItemChangeEvent event) {}
}
