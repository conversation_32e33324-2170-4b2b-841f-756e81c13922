package dev.debutter.weirdlycubed.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import dev.debutter.weirdlycubed.Main;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.tag.resolver.TagResolver;
import net.minestom.server.command.CommandSender;
import net.minestom.server.entity.Player;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Locale;
import java.util.stream.Collectors;

public class LocaleManager {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().disableHtmlEscaping().create();
    private static final HashMap<String, JsonObject> localeObjects = new HashMap<>();
    private static final Charset charset = StandardCharsets.ISO_8859_1;
    private static final String[] builtinLocales = new String[] {"en_us"};
    private static final String defaultLocale = "en_us";

    static {
        // Get every language saved in resources
        for (String locale : builtinLocales) {
            loadBuiltinLanguage(locale);
        }

        // Load other languages saved on the disk
        loadLanguages();
    }

    /**
     *  Loads a language stored in the resources and syncs it with the disk
     */
    private static void loadBuiltinLanguage(String locale) {
        String resourcePath = "/lang/" + locale + ".json";
        String filepath = "." + resourcePath;

        // Get language resource
        InputStream input = Main.class.getResourceAsStream(resourcePath);
        if (input == null) return;

        String resourceContent = new BufferedReader(new InputStreamReader(input, charset))
                .lines()
                .collect(Collectors.joining("\n"));
        JsonObject jsonResource = gson.fromJson(resourceContent, JsonObject.class);

        // Check to merge language content found on the disk
        File langFile = new File(filepath);
        String diskContent;
        JsonObject jsonDisk = null;

        if (langFile.exists()) {
            try (BufferedReader br = new BufferedReader(new FileReader(langFile))) {
                diskContent = br.lines().collect(Collectors.joining("\n"));
                jsonDisk = gson.fromJson(diskContent, JsonObject.class);
            } catch (IOException e) {
                ConsoleLogger.exception("Failed to read language file", e);
            }

            // Resolve language objects
            if (jsonDisk != null) {
                for (String key : jsonDisk.keySet()) {
                    jsonResource.add(key, jsonDisk.get(key));
                }
            }
        }

        // Save language file to disk
        try {
            langFile.getParentFile().mkdirs();
            Files.deleteIfExists(langFile.toPath());
            Files.writeString(langFile.toPath(), gson.toJson(jsonResource));
        } catch (IOException e) {
            ConsoleLogger.exception("Failed to save language file", e);
        }

        localeObjects.put(locale, jsonResource);
    }

    /**
     *  Loads additional unknown languages stored on the disk
     */
    private static void loadLanguages() {
        File langDir = new File("./lang");
        langDir.mkdirs();

        File[] langFiles = langDir.listFiles();
        if (langFiles != null) {
            for (File file : langFiles) {
                if (!file.isFile() || !file.canRead()) continue;

                String locale = AwesomerText.removeSuffix(file.getName(), ".json");
                if (localeObjects.containsKey(locale)) continue;

                JsonObject json = null;

                try (BufferedReader br = new BufferedReader(new FileReader(file))) {
                    String content = br.lines().collect(Collectors.joining("\n"));
                    json = gson.fromJson(content, JsonObject.class);
                } catch (IOException e) {
                    ConsoleLogger.exception("Failed to read language file", e);
                    continue;
                }

                if (json != null) {
                    localeObjects.put(locale, json);
                }
            }
        }
    }

    public static String getDefaultLocale() {
        return defaultLocale;
    }

    // Functions to get a raw locale message
    public static String getMessage(String key) {
        return getMessage(key, defaultLocale);
    }
    public static String getMessage(String key, Player player) {
        Locale locale = player.getLocale();
        return getMessage(key, locale == null ? defaultLocale : locale.toLanguageTag().toLowerCase());
    }
    public static String getMessage(String key, CommandSender sender) {
        if (sender instanceof Player) {
            return getMessage(key, (Player) sender);
        }
        return getMessage(key);
    }
    public static String getMessage(String key, String locale) {
        if (!localeObjects.containsKey(locale)) {
            if (!locale.equals(defaultLocale)) {
                return getMessage(key, defaultLocale);
            }
            return null;
        }
        JsonObject jsonLocale = localeObjects.get(locale);
        if (!jsonLocale.has(key)) {
            if (!locale.equals(defaultLocale)) {
                return getMessage(key, defaultLocale);
            }
            return null;
        }
        return jsonLocale.getAsJsonPrimitive(key).getAsString();
    }

    // Functions to get a beautified locale message
    public static Component getBeautifiedMessage(String key, TagResolver... tagResolvers) {
        return AwesomerText.beautifyMessage(getMessage(key), tagResolvers);
    }
    public static Component getBeautifiedMessage(String key, Player player, TagResolver... tagResolvers) {
        return AwesomerText.beautifyMessage(getMessage(key, player), tagResolvers);
    }
    public static Component getBeautifiedMessage(String key, CommandSender sender, TagResolver... tagResolvers) {
        return AwesomerText.beautifyMessage(getMessage(key, sender), tagResolvers);
    }
    public static Component getBeautifiedMessage(String key, String locale, TagResolver... tagResolvers) {
        return AwesomerText.beautifyMessage(getMessage(key, locale), tagResolvers);
    }
}
