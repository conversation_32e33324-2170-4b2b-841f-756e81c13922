package dev.debutter.weirdlycubed.blocks.types;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.inventories.menus.ChestMenu;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.objects.InstanceLoc;
import dev.debutter.weirdlycubed.utils.objects.ContainerItem;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.entity.Player;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.inventory.InventoryType;
import net.minestom.server.item.ItemStack;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.UnknownNullability;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

public class Chest extends CustomBlock {

    private static final Tag<List<ContainerItem>> contentsTag = Tag.Structure("contents", ContainerItem.class).list();
    private static final HashMap<InstanceLoc, ChestMenu> chestInventories = new HashMap<>();
    private static final CustomItemTypes itemDrop = CustomItemTypes.CHEST;

    public Chest() {
        super(Block.CHEST, "chest");
    }

    @NotNull
    @Override
    public Sound getBreakSound() {
        return Sound.sound(Key.key("block.wood.break"), Sound.Source.BLOCK, 1f, 0.8f);
    }

    @Override
    public int getMiningDuration() {
        return 40;
    }

    @Override
    public boolean onInteract(@NotNull Interaction interaction) {
        Player player = interaction.getPlayer();
        InstanceLoc chestLoc = new InstanceLoc(interaction.getInstance(), interaction.getBlockPosition());

        ChestMenu chestInventory = chestInventories.computeIfAbsent(chestLoc, k ->
            new ChestMenu(InventoryType.CHEST_3_ROW, AwesomerText.beautifyMessage("Chest"), chestLoc)
        );
        player.openInventory(chestInventory);
        return true;
    }

    @Override
    public ItemStack[] getItemDrops(@NotNull BlockHandler.Destroy destroy) {
        // Get current items directly from the inventory menu if available
        InstanceLoc loc = new InstanceLoc(destroy.getInstance(), destroy.getBlockPosition());
        @Nullable ChestMenu chestInventory = chestInventories.get(loc);

        if (chestInventory != null) {
            ItemStack[] itemDrops;

            ItemStack[] itemStacks = chestInventory.getItemStacks();
            itemDrops = Arrays.copyOf(itemStacks, itemStacks.length + 1);
            itemDrops[itemStacks.length] = itemDrop.getItemStack();

            return itemDrops;
        }

        // Otherwise, load the chest contents from the NBT data
        ContainerItem[] containerItems = loadFromNBT(destroy.getBlock());
        ItemStack[] itemDrops = new ItemStack[containerItems.length + 1];

        for (int i = 0; i < containerItems.length; i++) {
            itemDrops[i] = containerItems[i].item();
        }

        itemDrops[containerItems.length] = itemDrop.getItemStack();

        return itemDrops;
    }

    @Override
    public void onBreak(@NotNull Destroy destroy) {
        super.onBreak(destroy);

        // Remove chest inventory after breaking
        InstanceLoc chestLoc = new InstanceLoc(destroy.getInstance(), destroy.getBlockPosition());

        chestInventories.remove(chestLoc);
    }

    public static ContainerItem[] loadFromNBT(Block block) {
        @UnknownNullability List<ContainerItem> nbtContents = block.getTag(contentsTag);

        // If the contents is null, return the empty content
        if (nbtContents == null) return new ContainerItem[0];

        // Otherwise load the chest content from the nbt data
        List<ContainerItem> content = new ArrayList<>();

        for (ContainerItem containerItem : nbtContents) {
            int slot = containerItem.slot();
            ItemStack item = containerItem.item();

            content.add(containerItem);
        }

        return content.toArray(new ContainerItem[0]);
    }

    public static void saveToNBT(InstanceLoc loc, ChestMenu menu) {
        List<ContainerItem> contents = new ArrayList<>();

        for (int slot = 0; slot < menu.getSize(); slot++) {
            ItemStack item = menu.getItemStack(slot);

            // Skip air
            if (item.isAir()) continue;

            // Add container item
            contents.add(new ContainerItem(slot, item));
        }

        // Update the nbt of the block
        loc.instance().setBlock(loc.point(), loc.block().withTag(contentsTag, contents));
    }

}
