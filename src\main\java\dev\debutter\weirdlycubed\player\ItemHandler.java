package dev.debutter.weirdlycubed.player;

import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.coordinate.Pos;
import net.minestom.server.coordinate.Vec;
import net.minestom.server.entity.ItemEntity;
import net.minestom.server.entity.Player;
import net.minestom.server.event.item.ItemDropEvent;
import net.minestom.server.event.item.PickupItemEvent;
import net.minestom.server.instance.Instance;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.time.Duration;

public class ItemHandler {

    public static void onPlayerDropItem(@NotNull ItemDropEvent event) {
        Player player = event.getPlayer();
        ItemStack droppedItem = event.getItemStack();

        Instance instance = player.getInstance();
        Pos position = player.getPosition().add(0, player.getEyeHeight(), 0);

        ItemEntity itemEntity = new ItemEntity(droppedItem);
        itemEntity.setInstance(instance, position);

        Vec playerDirection = player.getPosition().direction().mul(5);

        itemEntity.setVelocity(playerDirection);
        itemEntity.setPickupDelay(Duration.ofSeconds(1));
    }

    public static void onPlayerPickupItem(@NotNull PickupItemEvent event) {
        Player player = (Player) event.getEntity();
        ItemEntity itemEntity = event.getItemEntity();
        ItemStack itemStack = itemEntity.getItemStack();

        player.getInventory().addItemStack(itemStack);
        itemEntity.remove();

        Sound itemPickupSound = Sound.sound(Key.key("entity.item.pickup"), Sound.Source.PLAYER, 0.5f, 1f);
        player.getInstance().playSound(itemPickupSound, player.getPosition());
    }
}
