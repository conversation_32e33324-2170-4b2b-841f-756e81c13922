package dev.debutter.weirdlycubed.blocks.types;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import net.kyori.adventure.key.Key;
import net.kyori.adventure.sound.Sound;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;

public class GenericBlock extends CustomBlock {

    private final CustomItemTypes itemDrop;
    private final int miningDuration;
    private final String soundKey;
    private final float soundPitch;

    public GenericBlock(Block block, String id, CustomItemTypes itemDrop, int miningDuration, String soundKey, float soundPitch) {
        super(block, id);

        this.itemDrop = itemDrop;
        this.miningDuration = miningDuration;
        this.soundKey = soundKey;
        this.soundPitch = soundPitch;
    }

    @Override
    public ItemStack[] getItemDrops(@NotNull BlockHandler.Destroy destroy) {
        return new ItemStack[] { itemDrop.getItemStack() };
    }

    @NotNull
    @Override
    public Sound getBreakSound() {
        return Sound.sound(Key.key(soundKey), Sound.Source.BLOCK, 1f, soundPitch);
    }

    @Override
    public int getMiningDuration() {
        return miningDuration;
    }

}
