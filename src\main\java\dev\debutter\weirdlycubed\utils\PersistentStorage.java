package dev.debutter.weirdlycubed.utils;

import net.kyori.adventure.nbt.BinaryTagIO;
import net.kyori.adventure.nbt.CompoundBinaryTag;
import net.minestom.server.tag.TagHandler;
import net.minestom.server.utils.nbt.BinaryTagWriter;
import org.jetbrains.annotations.NotNull;

import java.io.*;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.HashSet;

public record PersistentStorage(Path directory, String name) {

    /** Set of data entries that have been loaded during this runtime session */
    private static final HashSet<PersistentStorage> loadedEntries = new HashSet<>();
    /** Map of data entries that have been loaded and not saved to disk */
    private static final HashMap<PersistentStorage, TagHandler> liveEntries = new HashMap<>();

    public static boolean flushAll() {
        boolean success = true;

        for (PersistentStorage entry : loadedEntries) {
            if (!entry.flush()) success = false;
        }

        return success;
    }

    public boolean mkDirs() {
        return directory.toFile().mkdirs();
    }
    public File liveFile() {
        return directory.resolve(name + ".dat_new").toFile();
    }
    public File coldFile() {
        return directory.resolve(name + ".dat").toFile();
    }

    /**
     * Reads the data entry and returns a modifiable tag handler
     */
    public @NotNull TagHandler read() {
        // delete unflushed data
        if (!loadedEntries.contains(this)) {
            // Delete the live data file if it wasn't loaded during this runtime session
            liveFile().delete();
        }
        loadedEntries.add(this);

        // Get data from cache if available
        if (liveEntries.containsKey(this)) return liveEntries.get(this);

        // Try reading the data file
        mkDirs();

        File file = liveFile().exists() ? liveFile() : coldFile();

        try {
            InputStream in = new DataInputStream(new FileInputStream(file));
            CompoundBinaryTag data = BinaryTagIO.reader().readNameless(in);

            TagHandler tagHandler = TagHandler.fromCompound(data);
            liveEntries.put(this, tagHandler);
            return tagHandler;
        } catch (IOException e) {
            ConsoleLogger.warning("Failed to read data for " + this);
        }

        // Otherwise create an empty nbt object
        TagHandler tagHandler = TagHandler.newHandler();
        liveEntries.put(this, tagHandler);
        return tagHandler;
    }

    /**
     * Saves the data entry to disk in an unflushed state
     * Useful to reduce memory usage of data entries that are not actively being used
     */
    public boolean save() {
        File liveFile = liveFile();
        TagHandler tagHandler = liveEntries.get(this);

        // Cancel if no tag handler exists
        if (tagHandler == null) {
            ConsoleLogger.warning("No data file for " + this + " was found in the cache");
            return false;
        }

        // Write the data to a live file
        mkDirs();

        try {
            DataOutputStream out = new DataOutputStream(new FileOutputStream(liveFile));
            BinaryTagWriter tagWriter = new BinaryTagWriter(out);

            tagWriter.writeNameless(tagHandler.asCompound());
        } catch (IOException e) {
            ConsoleLogger.exception("Failed to save data file", e);
            return false;
        }

        // Remove the tag handler from memory
        liveEntries.remove(this);
        return true;
    }

    /**
     * Finalizes the data entry and saves it to cold storage
     */
    public boolean flush() {
        // Save the data to create the live file
        if (!save()) return false;

        // Replace the cold file with the live file
        File liveFile = liveFile();
        File coldFile = coldFile();

        try {
            if (coldFile.exists()) coldFile.delete();

            liveFile.renameTo(coldFile);
        } catch (Exception e) {
            ConsoleLogger.exception("Failed to flush data", e);
            return false;
        }
        return true;
    }

}
