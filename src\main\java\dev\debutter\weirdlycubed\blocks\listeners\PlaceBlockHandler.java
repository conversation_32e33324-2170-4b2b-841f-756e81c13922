package dev.debutter.weirdlycubed.blocks.listeners;

import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import dev.debutter.weirdlycubed.utils.ItemHelper;
import net.minestom.server.coordinate.Point;
import net.minestom.server.entity.Player;
import net.minestom.server.entity.PlayerHand;
import net.minestom.server.event.player.PlayerBlockPlaceEvent;
import net.minestom.server.instance.Instance;
import net.minestom.server.item.ItemStack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class PlaceBlockHandler {

    public static void onPlaceBlock(@NotNull PlayerBlockPlaceEvent event) {
        Player player = event.getPlayer();
        PlayerHand hand = event.getHand();
        Instance instance = event.getInstance();
        ItemStack usedItem = player.getItemInHand(hand);
        Point position = event.getBlockPosition();

        event.setCancelled(true);

        // Get custom block item
        @Nullable CustomBlockTypes customBlockType = ItemHelper.getCustomBlockItem(usedItem);
        if (customBlockType == null) return;

        // Cancel if the block cannot be placed there
        if (!customBlockType.getHandler().canPlace(instance, position)) return;

        // Place the custom block
        instance.setBlock(position, customBlockType.getBlock());

        final ItemStack newUsedItem = usedItem.consume(1);
        player.setItemInHand(hand, newUsedItem);
    }

}
