package dev.debutter.weirdlycubed.player;

import dev.debutter.weirdlycubed.blocks.listeners.BreakBlockHandler;
import net.minestom.server.entity.Player;
import net.minestom.server.entity.damage.DamageType;
import net.minestom.server.event.player.PlayerTickEvent;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.UUID;

import static dev.debutter.weirdlycubed.server.CustomTabList.updateTabList;

public class PlayerTicker {

    private static final float defaultFlightSpeed = 0.05f;
    private static HashMap<UUID, Integer> flightSpeedFactor = new HashMap<>();
    private static final int DAMAGE_AMOUNT = 1;

    public static void onTick(@NotNull PlayerTickEvent event) {
        Player player = event.getPlayer();

        updateTabList(player);
        updateFlightSpeed(player);
        BreakBlockHandler.tickMining(player);
        checkPlayerInVoid(player);
    }

    /** Ticks a unique pair of two players */
    public static void tickPair(Player player1, Player player2) {
        collisionCheck(player1, player2);
    }

    private static void updateFlightSpeed(Player player) {
        if (!player.isAllowFlying()) {
            return;
        }

        // Reset flight speed when the player stops sprinting
        if (!player.isSprinting() || !player.isFlying()) {
            if (player.getFlyingSpeed() != defaultFlightSpeed) {
                player.setFlyingSpeed(defaultFlightSpeed);
                flightSpeedFactor.put(player.getUuid(), 0);
            }
            return;
        }

        // Increment the players flight speed factor
        int speedFactor = flightSpeedFactor.getOrDefault(player.getUuid(), 0);
        if (speedFactor >= 115) return;

        speedFactor += 1;
        flightSpeedFactor.put(player.getUuid(), speedFactor);

        // Apply new flight speed
        float multiplier = (speedFactor * speedFactor * 3f + 10_000f) / 10_000f;
        float newFlightSpeed = defaultFlightSpeed * multiplier;

        if (player.getFlyingSpeed() != newFlightSpeed) {
            player.setFlyingSpeed(newFlightSpeed);
        }
    }

    private static void checkPlayerInVoid(Player player) {
        if (player.getPosition().y() < -128) {
            player.damage(DamageType.GENERIC, DAMAGE_AMOUNT);
        }
    }

    private static void collisionCheck(Player player1, Player player2) {
        var bBox = player1.getBoundingBox();
        var bBox2 = player2.getBoundingBox();

        double minX1 = player1.getPosition().x() + bBox.minX();
        double maxX1 = player1.getPosition().x() + bBox.maxX();
        double minZ1 = player1.getPosition().z() + bBox.minZ();
        double maxZ1 = player1.getPosition().z() + bBox.maxZ();

        double minX2 = player2.getPosition().x() + bBox2.minX();
        double maxX2 = player2.getPosition().x() + bBox2.maxX();
        double minZ2 = player2.getPosition().z() + bBox2.minZ();
        double maxZ2 = player2.getPosition().z() + bBox2.maxZ();

        boolean collisionX = maxX1 > minX2 && minX1 < maxX2;
        boolean collisionZ = maxZ1 > minZ2 && minZ1 < maxZ2;

        if (collisionX && collisionZ) {
            var position1 = player1.getPosition();
            var position2 = player2.getPosition();

            double deltaX = position1.x() - position2.x();
            double deltaZ = position1.z() - position2.z();

            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            if (distance == 0) {
                return;
            }

            double normalX = deltaX / distance;
            double normalZ = deltaZ / distance;

            double pushFactor = 0.1;
            player1.setVelocity(player1.getVelocity().add(normalX * pushFactor, 0, normalZ * pushFactor));
            player2.setVelocity(player2.getVelocity().add(-normalX * pushFactor, 0, -normalZ * pushFactor));
        }
    }

}
