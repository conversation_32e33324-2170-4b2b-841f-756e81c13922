* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #121212;
    color: #e0e0e0;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.header {
    background-color: #1e1e1e;
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #333;
}

.main-content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
}

.console-section {
    flex: 2;
    min-width: 300px;
    margin: 10px;
    background-color: #1e1e1e;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
}

.console-section h2 {
    text-align: center;
    padding: 10px;
    border-bottom: 1px solid #333;
}

.console {
    flex: 1;
    background-color: #111;
    border-radius: 0 0 10px 10px;
    padding: 10px;
    overflow-y: auto;
    overflow-x: hidden;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.4;
}

.players-section {
    flex: 1;
    min-width: 250px;
    margin: 10px;
    background-color: #1e1e1e;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
}

.players-section h2 {
    text-align: center;
    padding: 10px;
    border-bottom: 1px solid #333;
}

.player-list {
    list-style: none;
    padding: 10px;
    overflow-y: auto;
    flex: 1;
}

.player-list li {
    display: flex;
    align-items: center;
    background-color: #2a2a2a;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 5px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.player-list li:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.player-list li img {
    border-radius: 50%;
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border: 2px solid #444;
}

.player-list li span {
    font-size: 16px;
}

.footer {
    text-align: center;
    padding: 10px;
    background-color: #1e1e1e;
    border-top: 1px solid #333;
}

@media (max-width: 800px) {
    .main-content {
        flex-direction: column;
    }

    .console-section, .players-section {
        width: 100%;
        height: auto;
    }
}