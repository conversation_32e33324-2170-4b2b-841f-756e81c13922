package dev.debutter.weirdlycubed.utils;

import dev.debutter.weirdlycubed.blocks.CustomBlock;
import dev.debutter.weirdlycubed.blocks.CustomBlockTypes;
import dev.debutter.weirdlycubed.items.CustomItemTypes;
import dev.debutter.weirdlycubed.items.ItemBuilder;
import net.minestom.server.component.DataComponents;
import net.minestom.server.item.ItemStack;
import net.minestom.server.item.component.CustomData;
import org.jetbrains.annotations.Nullable;

public class ItemHelper {

    public static @Nullable CustomBlockTypes getCustomBlockItem(ItemStack itemStack) {
        // Get custom block item tag
        CustomData customData = itemStack.get(DataComponents.CUSTOM_DATA);
        if (customData == null) return null;

        String customBlockId = customData.getTag(CustomBlock.BLOCK_ITEM_TAG);
        return CustomBlockTypes.getFromId(customBlockId);
    }

    public static @Nullable CustomItemTypes getCustomItem(ItemStack itemStack) {
        // Get custom item tag
        CustomData customData = itemStack.get(DataComponents.CUSTOM_DATA);
        if (customData == null) return null;

        String customItemId = customData.getTag(ItemBuilder.ITEM_TAG);
        return CustomItemTypes.getFromId(customItemId);
    }

}
