package dev.debutter.weirdlycubed.server;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import dev.debutter.weirdlycubed.utils.ConsoleLogger;
import org.jetbrains.annotations.Nullable;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public record OppedPlayer(String uuid, String username) {

    private static final String FILE_PATH = "config/ops.json";

    /**
     * @return an array of OppedPlayer
     */
    public static OppedPlayer[] getAllOps() {
        try (FileReader reader = new FileReader(FILE_PATH)) {
            return new Gson().fromJson(reader, OppedPlayer[].class);
        } catch (IOException e) {
            ConsoleLogger.exception(e);
            return new OppedPlayer[0];
        }
    }

    /**
     * @param ops The new list of ops to be saved.
     */
    public static void saveAllOps(OppedPlayer[] ops) {
        try (Writer writer = new FileWriter(FILE_PATH)) {
            new GsonBuilder().setPrettyPrinting().create().toJson(ops, writer);
        } catch (IOException e) {
            ConsoleLogger.exception(e);
        }
    }

    /**
     * @param uuid The UUID of the player to find.
     * @return The OppedPlayer or null if none found.
     */
    public static @Nullable OppedPlayer getOp(UUID uuid) {
        OppedPlayer[] currentOps = getAllOps();
        for (OppedPlayer op : currentOps) {
            if (op.uuid().equals(uuid.toString())) {
                return op;
            }
        }
        return null;
    }

    /**
     * @param uuid The UUID to check.
     * @return True if the player is op, false otherwise.
     */
    public static boolean isPlayerOp(UUID uuid) {
        return getOp(uuid) != null;
    }

    /**
     * @param uuid
     * @param username
     */
    public static void opPlayer(UUID uuid, String username) {
        OppedPlayer[] currentOps = getAllOps();

        for (OppedPlayer op : currentOps) {
            if (op.uuid().equals(uuid.toString())) {
                return;
            }
        }

        OppedPlayer[] newOps = new OppedPlayer[currentOps.length + 1];
        System.arraycopy(currentOps, 0, newOps, 0, currentOps.length);
        newOps[currentOps.length] = new OppedPlayer(uuid.toString(), username);

        saveAllOps(newOps);
    }

    /**
     * @param uuid The UUID of the player to remove.
     */
    public static void deopPlayer(UUID uuid) {
        OppedPlayer[] currentOps = getAllOps();
        List<OppedPlayer> updatedOpsList = new ArrayList<>();

        for (OppedPlayer op : currentOps) {
            if (!op.uuid().equals(uuid.toString())) {
                updatedOpsList.add(op);
            }
        }

        if (updatedOpsList.size() != currentOps.length) {
            OppedPlayer[] updatedOps = updatedOpsList.toArray(new OppedPlayer[0]);
            saveAllOps(updatedOps);
        }
    }
}
