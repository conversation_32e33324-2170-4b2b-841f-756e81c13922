package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.PermissionsHandler;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.builder.Command;

public class StopCmd extends Command {

    public StopCmd() {
        super("stop");

        setDefaultExecutor((sender, context) -> {
            if (!PermissionsHandler.canUseStopCommand(sender.identity().uuid())) {
                sender.sendMessage(Component.text("You do not have permission to use this command.", NamedTextColor.RED));
                return;
            }

            sender.sendMessage(Component.text("Stopping server...", NamedTextColor.GREEN));
            MinecraftServer.stopCleanly();
        });
    }
}
