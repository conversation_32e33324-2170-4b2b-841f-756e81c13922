package dev.debutter.weirdlycubed.utils;

import dev.debutter.weirdlycubed.Constants;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.*;

public class ConsoleLogger {

    private static final Logger LOGGER = Logger.getLogger(Constants.PROJECT_NAME);
    private static final String format = "<%1$s>[<timestamp>] [<level>]</%1$s> <white><message><reset>\n";
    private static final String defaultColor = "#f0f6fc";
    private static final HashMap<Level, String> levelColors = new HashMap<>();
    private static final Queue<String> logBuffer = new ConcurrentLinkedQueue<>();

    static {
        levelColors.put(Level.INFO, "#79c0ff");
        levelColors.put(Level.WARNING, "#ffa657");
        levelColors.put(Level.SEVERE, "#ff7b72");

        ConsoleHandler handler = new ConsoleHandler();
        handler.setFormatter(new SimpleFormatter() {
            @Override
            public synchronized String format(LogRecord record) {
                Level level = record.getLevel();
                String color = levelColors.getOrDefault(level, defaultColor);
                String formattedMessage = AwesomerText.beautifyAnsi(
                        String.format(format, color),
                        Placeholder.unparsed("timestamp", String.valueOf(Calendar.getInstance().getTime())),
                        Placeholder.unparsed("level", level.getLocalizedName()),
                        Placeholder.parsed("message", record.getMessage())
                );
                logBuffer.add(formattedMessage);
                return formattedMessage;
            }
        });
        LOGGER.addHandler(handler);
        LOGGER.setUseParentHandlers(false);
    }

    public static void info(String message) {
        LOGGER.info(message);
    }

    public static void warning(String message) {
        LOGGER.warning(message);
    }

    public static void severe(String message) {
        LOGGER.severe(message);
    }

    public static void exception(String message, Throwable err) {
        LOGGER.log(Level.SEVERE, message, err);
    }
    public static void exception(Throwable err) {
        LOGGER.log(Level.SEVERE, err.getMessage(), err);
    }

    public static void logPlayerJoin(String playerName) {
        info(playerName + " has joined the server!");
    }

    public static void logPlayerConnect(String playerName) {
        info(playerName + " is establishing a connection to the server!");
    }

    public static void logPlayerDisconnect(String playerName) {
        info(playerName + " has left the server!");
    }

    public static void logPlayerChat(String playerName, String message) {
        info("\\<" + playerName + "> " + message);
    }

    public static void logCommandExecution(String playerName, String commandName) {
        info(playerName + " ran command: /" + commandName);
    }

    public static String[] getAndClearLogBuffer() {
        java.util.List<String> logs = new java.util.ArrayList<>();
        while (!logBuffer.isEmpty()) {
            logs.add(logBuffer.poll());
        }
        return logs.toArray(new String[0]);
    }
}
