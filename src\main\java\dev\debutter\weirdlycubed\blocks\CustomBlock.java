package dev.debutter.weirdlycubed.blocks;

import net.kyori.adventure.key.Key;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.block.Block;
import net.minestom.server.instance.block.BlockHandler;
import net.minestom.server.tag.Tag;
import org.jetbrains.annotations.NotNull;

public abstract class Custom<PERSON>lock implements Block<PERSON><PERSON>ler, CustomBlockHandler {

    /** The item tag used inside the CustomData component to denote what custom block should be placed */
    public static final Tag<String> BLOCK_ITEM_TAG = Tag.String("custom_block");

    private final Block defaultBlock;
    private final String id;

    public CustomBlock(Block block, String id) {
        this.defaultBlock = block;
        this.id = id;
    }

    public final @NotNull String getId() {
        return id;
    }
    public final Block getBlock() {
        return defaultBlock.withHandler(this);
    }

    @Override
    public final @NotNull Key getKey() {
        return Key.key("weirdlycubed:" + id);
    }

    @Override
    public final void onDestroy(@NotNull Destroy destroy) {
        Instance instance = destroy.getInstance();
        Block block = destroy.getBlock();
        Block newBlock = instance.getBlock(destroy.getBlockPosition());

        // If the old block is replaceable with the new block, trigger a replace event instead
        if (
            block.handler() instanceof CustomBlock customBlock &&
            newBlock.handler() instanceof CustomBlock newCustomBlock &&
            customBlock.isReplaceableWith(newCustomBlock)
        ) {
            this.onReplace(destroy);
            return;
        }

        // Otherwise the block will trigger a break event
        this.onBreak(destroy);
    }

//    @Override
//    public void onPlace(@NotNull Placement placement) {
//        if (placement instanceof PlayerPlacement) {
//            return;
//        }
//        Block block = placement.getBlock();
//        ConsoleLogger.info("The block " + block.name() + " has been placed");
//    }
//
//    @Override
//    public void onDestroy(@NotNull Destroy destroy) {
//        MinecraftServer.getSchedulerManager().scheduleNextProcess(() -> destroy.getInstance().setBlock(destroy.getBlockPosition(), Block.AMETHYST_BLOCK));
//
//        ConsoleLogger.info("The block " + destroy.getBlock().name() + " has been destroyed");
//    }
//
//    @Override
//    public @NotNull NamespaceID getNamespaceId() {
//        // Namespace required for serialization purpose
//        return NamespaceID.from("minestom:demo");
//    }
}