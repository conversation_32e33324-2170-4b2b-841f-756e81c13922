package dev.debutter.weirdlycubed.instances.generators;

import net.minestom.server.instance.Chunk;
import net.minestom.server.instance.Instance;
import net.minestom.server.instance.LightingChunk;
import net.minestom.server.instance.generator.Generator;
import net.minestom.server.utils.chunk.ChunkSupplier;
import org.jetbrains.annotations.NotNull;

public abstract class WorldGen extends SeededGen implements Generator, ChunkSupplier {

    public WorldGen(long seed) {
        super(seed);
    }

    @Override
    public @NotNull Chunk createChunk(@NotNull Instance instance, int x, int z) {
        return new LightingChunk(instance, x, z);
    }

}