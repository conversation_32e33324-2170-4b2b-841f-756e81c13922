package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.data.PlayerRuntimeData;
import dev.debutter.weirdlycubed.player.data.PlayerMode;
import dev.debutter.weirdlycubed.utils.LocaleManager;
import net.minestom.server.command.builder.Command;
import net.minestom.server.entity.Player;

public class SurvivalCmd extends Command {

    public SurvivalCmd() {
        super("survival");

        setDefaultExecutor((sender, context) -> {
            if (!(sender instanceof Player player)) {
                sender.sendMessage(LocaleManager.getBeautifiedMessage("commands.players_only", sender));
                return;
            }

            PlayerRuntimeData.setPlayerMode(player, PlayerMode.SURVIVAL);
            player.sendMessage(LocaleManager.getBeautifiedMessage("commands.survival.activate", player));
        });
    }
}
