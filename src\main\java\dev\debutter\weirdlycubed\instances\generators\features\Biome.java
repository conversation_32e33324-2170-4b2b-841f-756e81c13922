package dev.debutter.weirdlycubed.instances.generators.features;

import dev.debutter.weirdlycubed.instances.generators.SeededGen;
import net.minestom.server.coordinate.Point;
import net.minestom.server.instance.generator.GenerationUnit;

public abstract class Biome extends SeededGen {

    public Biome(long seed) {
        super(seed);
    }

    /// Method to generate terrain specific to the biome
    public abstract void generateTerrain(GenerationUnit unit, Point bottom, int stoneHeight, int waterLevel, boolean favorLower);
}
