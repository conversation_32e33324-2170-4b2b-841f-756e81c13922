package dev.debutter.weirdlycubed.inventories;

import net.kyori.adventure.text.Component;
import net.minestom.server.event.inventory.*;
import net.minestom.server.inventory.Inventory;
import net.minestom.server.inventory.InventoryType;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;

public abstract class InvMenu extends Inventory {

    private static HashMap<Inventory, InvMenu> menus = new HashMap<>();

    public InvMenu(@NotNull InventoryType inventoryType, @NotNull Component title) {
        super(inventoryType, title);

        menus.put(this, this);
    }

    public InvMenu(@NotNull InventoryType inventoryType, @NotNull String title) {
        super(inventoryType, title);

        menus.put(this, this);
    }

    protected abstract void onClose(@NotNull InventoryCloseEvent event);
    protected abstract void onClick(@NotNull InventoryClickEvent event);
    protected abstract void onOpen(@NotNull InventoryOpenEvent event);
    protected abstract void onPreClick(@NotNull InventoryPreClickEvent event);
    protected abstract void onItemChange(@NotNull InventoryItemChangeEvent event);

    public static void onInventoryClose(@NotNull InventoryCloseEvent event) {
        @Nullable InvMenu menu = menus.getOrDefault(event.getInventory(), null);
        if (menu != null) menu.onClose(event);
    }

    public static void onInventoryClick(@NotNull InventoryClickEvent event) {
        @Nullable InvMenu menu = menus.getOrDefault(event.getInventory(), null);
        if (menu != null) menu.onClick(event);
    }

    public static void onInventoryOpen(@NotNull InventoryOpenEvent event) {
        @Nullable InvMenu menu = menus.getOrDefault(event.getInventory(), null);
        if (menu != null) menu.onOpen(event);
    }

    public static void onInventoryPreClick(@NotNull InventoryPreClickEvent event) {
        @Nullable InvMenu menu = menus.getOrDefault(event.getInventory(), null);
        if (menu != null) menu.onPreClick(event);
    }

    public static void onInventoryItemChange(@NotNull InventoryItemChangeEvent event) {
        @Nullable InvMenu menu = menus.getOrDefault(event.getInventory(), null);
        if (menu != null) menu.onItemChange(event);
    }

}
