package dev.debutter.weirdlycubed.commands;

import dev.debutter.weirdlycubed.player.PermissionsHandler;
import dev.debutter.weirdlycubed.server.OppedPlayer;
import dev.debutter.weirdlycubed.utils.AwesomerText;
import dev.debutter.weirdlycubed.utils.Garden;
import dev.debutter.weirdlycubed.utils.objects.MojangResponse;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.minestom.server.MinecraftServer;
import net.minestom.server.command.CommandSender;
import net.minestom.server.command.builder.Command;
import net.minestom.server.command.builder.arguments.Argument;
import net.minestom.server.command.builder.arguments.ArgumentType;
import net.minestom.server.entity.Player;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;
import java.util.UUID;

public class OpCmd extends Command {

    public OpCmd() {
        super("op");

        setDefaultExecutor((sender, context) ->
                sender.sendMessage(Component.text("Usage: /op <add|remove|list> <player>", NamedTextColor.GRAY))
        );

        Argument<String> actionArg = ArgumentType.Word("action").from("add", "remove", "list");
        Argument<String> playerArg = ArgumentType.Word("player").setDefaultValue("none");

        addSyntax((sender, context) -> {
            String action = context.get(actionArg);
            String targetName = context.get(playerArg);

            if (action.equalsIgnoreCase("list")) {
                listOps(sender);
                return;
            }

            if (!canManageOps(sender)) {
                sender.sendMessage(Component.text("You do not have permission to modify ops.", NamedTextColor.RED));
                return;
            }

            if (targetName.equals("none")) {
                sender.sendMessage(Component.text("Usage: /op <add|remove> <player>", NamedTextColor.GRAY));
                return;
            }

            switch (action) {
                case "add" -> opPlayerLogic(sender, targetName);
                case "remove" -> deopPlayerLogic(sender, targetName);
                default -> sender.sendMessage(Component.text("Unknown action: " + action, NamedTextColor.RED));
            }
        }, actionArg, playerArg);
    }

    private void opPlayerLogic(CommandSender sender, String targetName) {
        Player targetOnline = MinecraftServer.getConnectionManager().getOnlinePlayerByUsername(targetName);
        if (targetOnline != null) {
            addOp(sender, targetOnline.getUuid(), targetOnline.getUsername());
        } else {
            @Nullable MojangResponse.User userData = Garden.getMojangData(targetName);
            if (userData == null) {
                sender.sendMessage(AwesomerText.beautifyMessage("<red>Could not fetch UUID for player '" + targetName + "'."));
            } else {
                UUID fetchedUuid = userData.id();
                String fetchedUsername = userData.username();

                addOp(sender, fetchedUuid, fetchedUsername);
            }
        }
    }

    private void addOp(CommandSender sender, UUID uuid, String username) {
        if (OppedPlayer.isPlayerOp(uuid)) {
            sender.sendMessage(Component.text("Player '" + username + "' is already OP.", NamedTextColor.YELLOW));
        } else {
            OppedPlayer.opPlayer(uuid, username);
            sender.sendMessage(Component.text("Player '" + username + "' has been opped.", NamedTextColor.GREEN));
        }
    }

    private void deopPlayerLogic(CommandSender sender, String targetName) {
        Player targetOnline = MinecraftServer.getConnectionManager().getOnlinePlayerByUsername(targetName);
        if (targetOnline != null) {
            removeOp(sender, targetOnline.getUuid(), targetOnline.getUsername());
        } else {
            @Nullable MojangResponse.User userData = Garden.getMojangData(targetName);
            if (userData == null) {
                sender.sendMessage(AwesomerText.beautifyMessage("<red>Could not fetch UUID for player '" + targetName + "'."));
            } else {
                UUID fetchedUuid = userData.id();
                String fetchedUsername = userData.username();

                removeOp(sender, fetchedUuid, fetchedUsername);
            }
        }
    }

    private void removeOp(CommandSender sender, UUID uuid, String username) {
        if (!OppedPlayer.isPlayerOp(uuid)) {
            sender.sendMessage(Component.text("Player '" + username + "' is not OP.", NamedTextColor.YELLOW));
        } else {
            OppedPlayer.deopPlayer(uuid);
            sender.sendMessage(Component.text("Player '" + username + "' has been de-opped.", NamedTextColor.GREEN));
        }
    }

    private void listOps(CommandSender sender) {
        OppedPlayer[] ops = OppedPlayer.getAllOps();
        if (ops.length == 0) {
            sender.sendMessage(Component.text("No players are currently OP.", NamedTextColor.YELLOW));
            return;
        }

        sender.sendMessage(AwesomerText.beautifyMessage("<green>Currently OP players:"));
        Arrays.stream(ops).forEach(opped ->
                sender.sendMessage(Component.text(" - " + opped.username() + " (" + opped.uuid() + ")", NamedTextColor.GREEN))
        );
    }

    private boolean canManageOps(CommandSender sender) {
        return PermissionsHandler.isOp(sender.identity().uuid());
    }
}
