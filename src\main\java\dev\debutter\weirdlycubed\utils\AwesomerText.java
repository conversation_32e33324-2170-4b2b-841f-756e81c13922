package dev.debutter.weirdlycubed.utils;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.minimessage.tag.resolver.TagResolver;
import net.kyori.adventure.text.serializer.ansi.ANSIComponentSerializer;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
import net.kyori.ansi.ColorLevel;

public class AwesomerText {

    private static final ANSIComponentSerializer AnsiSerializer =
            ANSIComponentSerializer.builder()
                    .colorLevel(ColorLevel.TRUE_COLOR) // TODO: automatically figure out what color level the console is
                    .build();

    public static Component stylize(String string) {
        return MiniMessage.miniMessage().deserialize(string);
    }

    public static String destylize(Component component) {
        return MiniMessage.miniMessage().serialize(component);
    }

    public static String stripStyles(Component component) {
        return PlainTextComponentSerializer.plainText().serialize(component);
    }

    /**
     * Beautify a message using the MiniMessage format and placeholders
     * @param message Input message
     * @param tagResolvers Component placeholders
     * @return Beautified message
     */
    public static Component beautifyMessage(String message, TagResolver... tagResolvers) {
        return MiniMessage.miniMessage().deserialize(message, tagResolvers);
    }

    /**
     * Beautify an ansi string using the MiniMessage format and player imports
     * @param text Input message
     * @param tagResolvers Component placeholders
     * @return Beautified message
     */
    public static String beautifyAnsi(String text, TagResolver... tagResolvers) {
        return AnsiSerializer.serialize(MiniMessage.miniMessage().deserialize(text, tagResolvers));
    }

    public static String removePrefix(String string, String prefix) {
        if (string.startsWith(prefix)) {
            return string.substring(prefix.length());
        }
        return string;
    }

    public static String removeSuffix(String string, String suffix) {
        if (string.endsWith(suffix)) {
            return string.substring(0, string.length() - suffix.length());
        }
        return string;
    }
}
