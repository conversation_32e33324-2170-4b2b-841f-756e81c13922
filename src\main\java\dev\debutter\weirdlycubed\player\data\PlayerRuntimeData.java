package dev.debutter.weirdlycubed.player.data;

import net.minestom.server.entity.GameMode;
import net.minestom.server.entity.Player;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class PlayerRuntimeData {
    private static final List<String> VALID_COLORS = Arrays.asList("black", "dark_green", "dark_aqua", "dark_red", "dark_purple", "gold", "gray", "dark_gray", "blue", "green", "aqua", "red", "light_purple", "yellow", "white");

    private static final HashMap<Player, PlayerMode> playerModes = new HashMap<>();
    private static final HashMap<Player, String> playerPrefixes = new HashMap<>();
    private static final HashMap<Player, String> playerChatColors = new HashMap<>();
    private static final HashMap<UUID, UUID> replyMap = new HashMap<>();

    public static PlayerMode getPlayerMode(Player player) {
        return playerModes.getOrDefault(player, PlayerMode.SURVIVAL);
    }
    public static void setPlayerMode(Player player, PlayerMode mode) {
        switch (mode) {
            case SURVIVAL -> {
                player.setGameMode(GameMode.SURVIVAL);
                player.setAllowFlying(false);
            }
            case GOD -> {
                player.setGameMode(GameMode.SURVIVAL);
                player.setAllowFlying(true);
            }
            case GHOST -> {
                player.setGameMode(GameMode.SPECTATOR);
                player.setAllowFlying(true);
            }
        }

        playerModes.put(player, mode);
    }

    public static void setPrefix(Player player, String prefix) {
        playerPrefixes.put(player, prefix);
    }

    public static String getPrefix(Player player) {
        return playerPrefixes.getOrDefault(player, "");
    }

    public static void setChatColor(Player player, String color) {
        if (!playerChatColors.containsKey(player) || !playerChatColors.get(player).equals(color)) {
            playerChatColors.put(player, color);
        }
    }

    public static String getChatColor(Player player) {
        return playerChatColors.getOrDefault(player, "");
    }

    public static void resetChatColor(Player player) {
        playerChatColors.remove(player);
    }

    public static void removeChatColor(Player player) {
        playerChatColors.remove(player);
    }

    public static boolean isValidColor(String color) {
        color = color.substring(1, color.length() - 1);
        if (color.startsWith("gradient:")) {
            String[] colorCodes = color.substring(9).split(":");
            for (String colorCode : colorCodes) {
                if (!colorCode.matches("#[0-9a-fA-F]{6}")) {
                    return false;
                }
            }
            return true;
        }
        if (color.startsWith("#")) {
            return color.matches("#[0-9a-fA-F]{6}");
        }
        return VALID_COLORS.contains(color);
    }

    public static void setReply(Player sender, Player target) {
        replyMap.put(target.getUuid(), sender.getUuid());
    }

    public static UUID getReply(Player player) {
        return replyMap.getOrDefault(player.getUuid(), null);
    }

    public static void removeReply(Player player) {
        replyMap.remove(player.getUuid());
    }
}