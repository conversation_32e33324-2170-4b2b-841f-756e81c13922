package dev.debutter.weirdlycubed.player;

import dev.debutter.weirdlycubed.server.OppedPlayer;
import net.minestom.server.entity.Player;

import java.util.UUID;

public class PermissionsHandler {

    /**
     * @param player The player to check
     * @return Whether the player is an operator
     */
    public static boolean isOp(Player player) {
        if (player == null) return false;
        UUID playerUUID = player.getUuid();
        return OppedPlayer.isPlayerOp(playerUUID);
    }

    /**
     * @param uuid The users uuid to check
     * @return Whether the user is an operator
     */
    public static boolean isOp(UUID uuid) {
        return OppedPlayer.isPlayerOp(uuid);
    }

    /**
     * @param player The player to op
     */
    public static void opPlayer(Player player) {
        if (player == null) return;
        OppedPlayer.opPlayer(player.getUuid(), player.getUsername());
    }

    /**
     * @param player The player to deop
     */
    public static void deopPlayer(Player player) {
        if (player == null) return;
        OppedPlayer.deopPlayer(player.getUuid());
    }

    /**
     * @param player The player to check
     * @return Whether the player has the permission to use the stop command
     */
    public static boolean canUseStopCommand(Player player) {
        return isOp(player);
    }

    /**
     * @param uuid The users uuid to check
     * @return Whether the user has the permission to use the stop command
     */
    public static boolean canUseStopCommand(UUID uuid) {
        return isOp(uuid);
    }
}
